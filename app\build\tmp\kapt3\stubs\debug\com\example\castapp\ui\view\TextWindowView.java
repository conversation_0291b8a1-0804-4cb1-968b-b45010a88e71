package com.example.castapp.ui.view;

/**
 * 文本窗口显示组件
 * 支持格式化文本显示和直接编辑功能
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0094\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010%\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b*\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0018\u0002\n\u0002\b\u001f\u0018\u00002\u00020\u0001B%\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u000e\u0010+\u001a\u00020\u00152\u0006\u0010,\u001a\u00020\u000fJ \u0010-\u001a\u00020\u00152\u0006\u0010.\u001a\u00020\u00072\u0006\u0010/\u001a\u00020\u00072\u0006\u00100\u001a\u00020\u0007H\u0002J\u000e\u00101\u001a\u00020\u00152\u0006\u00100\u001a\u00020\u0007J\"\u00102\u001a\u00020\u00152\u0006\u0010.\u001a\u00020\u00072\u0006\u0010/\u001a\u00020\u00072\b\u00103\u001a\u0004\u0018\u00010\nH\u0002J\u0010\u00104\u001a\u00020\u00152\b\u00103\u001a\u0004\u0018\u00010\nJ \u00105\u001a\u00020\u00152\u0006\u0010.\u001a\u00020\u00072\u0006\u0010/\u001a\u00020\u00072\u0006\u00106\u001a\u00020\u0007H\u0002J\u000e\u00107\u001a\u00020\u00152\u0006\u00106\u001a\u00020\u0007J\u001e\u00108\u001a\u00020\u00152\u0006\u00109\u001a\u00020\u000f2\u0006\u0010:\u001a\u00020\u000f2\u0006\u00106\u001a\u00020\u0007J\u0010\u0010;\u001a\u00020\u00152\u0006\u0010<\u001a\u00020\u001eH\u0002J\u000e\u0010=\u001a\u00020\u00152\u0006\u0010,\u001a\u00020\u000fJ \u0010>\u001a\u00020\u00152\u0006\u0010.\u001a\u00020\u00072\u0006\u0010/\u001a\u00020\u00072\u0006\u0010?\u001a\u00020\u001eH\u0002J\u000e\u0010@\u001a\u00020\u00152\u0006\u0010?\u001a\u00020\u001eJ\u000e\u0010A\u001a\u00020\u00152\u0006\u0010<\u001a\u00020\u001eJ(\u0010B\u001a\u00020\u00152\u0006\u0010.\u001a\u00020\u00072\u0006\u0010/\u001a\u00020\u00072\u0006\u0010C\u001a\u00020\u001e2\u0006\u0010D\u001a\u00020\u0007H\u0002J\u001e\u0010E\u001a\u00020\u00152\u0006\u0010,\u001a\u00020\u000f2\u0006\u0010C\u001a\u00020\u001e2\u0006\u0010D\u001a\u00020\u0007J(\u0010F\u001a\u00020\u00152\u0006\u0010.\u001a\u00020\u00072\u0006\u0010/\u001a\u00020\u00072\u0006\u0010G\u001a\u00020\u00072\u0006\u0010,\u001a\u00020\u000fH\u0002J\u000e\u0010H\u001a\u00020\u00152\u0006\u0010I\u001a\u00020\u0007J\u0006\u0010J\u001a\u00020\u0015J\u0018\u0010K\u001a\u00020\u00152\u0006\u0010.\u001a\u00020\u00072\u0006\u0010/\u001a\u00020\u0007H\u0002J\u0010\u0010L\u001a\u00020\'2\u0006\u0010M\u001a\u00020\u0007H\u0002J\u0006\u0010N\u001a\u00020\u0015J\u0006\u0010O\u001a\u00020\u0015J\u0010\u0010P\u001a\u00020\u00072\u0006\u0010Q\u001a\u00020\'H\u0002J\u0012\u0010R\u001a\u0004\u0018\u00010\n2\u0006\u0010S\u001a\u00020TH\u0002J\b\u0010U\u001a\u0004\u0018\u00010\nJ\u0006\u0010V\u001a\u00020\u0007J\u0006\u0010W\u001a\u00020\u000fJ\r\u0010X\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0002\u0010YJ\b\u0010Z\u001a\u0004\u0018\u00010\nJ\u0012\u0010[\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u000f0\\J\u0018\u0010]\u001a\u0014\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00070\u001dJ\u0006\u0010^\u001a\u00020\u001eJ\u0006\u0010_\u001a\u00020\u001eJ\u001a\u0010`\u001a\u0016\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u001dJ\u0006\u0010a\u001a\u00020\u0007J\u0012\u0010b\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00070\\J\u0018\u0010c\u001a\u00020\u00152\u0006\u0010d\u001a\u00020\u00072\u0006\u0010e\u001a\u00020\u0007H\u0002J\b\u0010f\u001a\u00020\u0015H\u0002J\u0018\u0010g\u001a\u00020\u000f2\u0006\u0010h\u001a\u00020T2\u0006\u0010i\u001a\u00020TH\u0002J\u0018\u0010j\u001a\u00020\u00152\u0006\u0010k\u001a\u00020\u00072\u0006\u0010l\u001a\u00020\u0007H\u0014J\u0010\u0010m\u001a\u00020\u000f2\u0006\u0010n\u001a\u00020oH\u0016J\b\u0010p\u001a\u00020\u000fH\u0016J\u0010\u0010q\u001a\u00020\u00072\u0006\u0010r\u001a\u00020\u0007H\u0002J\b\u0010s\u001a\u00020\u0015H\u0002J\u0018\u0010t\u001a\u00020\u00152\u0006\u0010.\u001a\u00020\u00072\u0006\u0010/\u001a\u00020\u0007H\u0002J\u000e\u0010u\u001a\u00020\u00152\u0006\u0010,\u001a\u00020\u000fJ\u0010\u0010v\u001a\u00020\u00152\b\u00103\u001a\u0004\u0018\u00010\nJ\u000e\u0010w\u001a\u00020\u00152\u0006\u0010x\u001a\u00020\u0007J\u000e\u0010y\u001a\u00020\u00152\u0006\u0010M\u001a\u00020\u0007J\u000e\u0010z\u001a\u00020\u00152\u0006\u0010,\u001a\u00020\u000fJ\"\u0010{\u001a\u00020\u00152\u001a\u0010|\u001a\u0016\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0015\u0018\u00010\u0014J\u001a\u0010}\u001a\u00020\u00152\u0012\u0010|\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00150\u0019JB\u0010~\u001a\u00020\u00152:\u0010|\u001a6\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u000f\u0012\u0006\u0012\u0004\u0018\u00010\u0007\u0012\u0018\u0012\u0016\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u001d\u0012\u0004\u0012\u00020\u00150\u001cJ \u0010\u007f\u001a\u00020\u00152\u0018\u0010|\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00150\u0014J\u001b\u0010\u0080\u0001\u001a\u00020\u00152\u0012\u0010|\u001a\u000e\u0012\u0004\u0012\u00020#\u0012\u0004\u0012\u00020\u00150\u0019J\u0010\u0010\u0081\u0001\u001a\u00020\u00152\u0007\u0010\u0082\u0001\u001a\u00020#J\u000f\u0010\u0083\u0001\u001a\u00020\u00152\u0006\u0010I\u001a\u00020\u0007J\u0017\u0010\u0084\u0001\u001a\u00020\u00152\u0006\u0010,\u001a\u00020\u000f2\u0006\u00100\u001a\u00020\u0007J\u0019\u0010\u0085\u0001\u001a\u00020\u00152\u0007\u0010\u0086\u0001\u001a\u00020\u00072\u0007\u0010\u0087\u0001\u001a\u00020\u0007J\t\u0010\u0088\u0001\u001a\u00020\u0015H\u0002J\t\u0010\u0089\u0001\u001a\u00020\u0015H\u0002J\t\u0010\u008a\u0001\u001a\u00020\u0015H\u0002J\t\u0010\u008b\u0001\u001a\u00020\u0015H\u0002J\t\u0010\u008c\u0001\u001a\u00020\u0015H\u0002J\u001b\u0010\u008d\u0001\u001a\u00020\u00152\u0007\u0010\u0086\u0001\u001a\u00020\u00072\u0007\u0010\u0087\u0001\u001a\u00020\u0007H\u0002R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\"\u0010\u0013\u001a\u0016\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0015\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0016\u001a\n\u0012\u0004\u0012\u00020\u0015\u0018\u00010\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0018\u001a\u0010\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u0015\u0018\u00010\u0019X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\"\u0010\u001a\u001a\u0016\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u0015\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000RD\u0010\u001b\u001a8\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u000f\u0012\u0006\u0012\u0004\u0018\u00010\u0007\u0012\u0018\u0012\u0016\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u001d\u0012\u0004\u0012\u00020\u0015\u0018\u00010\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R*\u0010\u001f\u001a\u001e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u000f\u0012\u0006\u0012\u0004\u0018\u00010\u0007\u0012\u0004\u0012\u00020\u0015\u0018\u00010 X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\"\u0010!\u001a\u0016\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0015\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\"\u001a\u0010\u0012\u0004\u0012\u00020#\u0012\u0004\u0012\u00020\u0015\u0018\u00010\u0019X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020#X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010%\u001a\u000e\u0012\u0004\u0012\u00020\'\u0012\u0004\u0012\u00020\u00070&X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010(\u001a\u0004\u0018\u00010)X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u008e\u0001"}, d2 = {"Lcom/example/castapp/ui/view/TextWindowView;", "Landroidx/appcompat/widget/AppCompatEditText;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "defStyleAttr", "", "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "currentFontFamily", "Lcom/example/castapp/utils/FontPresetManager$FontItem;", "currentFontSizeSp", "gestureDetector", "Landroid/view/GestureDetector;", "isBoldEnabled", "", "isInEditMode", "isItalicEnabled", "isWindowColorEnabled", "onBorderResizeListener", "Lkotlin/Function2;", "", "onDoubleClickListener", "Lkotlin/Function0;", "onEditStateChangeListener", "Lkotlin/Function1;", "onSelectionChangeListener", "onSelectionChangeWithAllFormatsListener", "Lkotlin/Function4;", "Lkotlin/Triple;", "", "onSelectionChangeWithColorListener", "Lkotlin/Function3;", "onSizeChangeListener", "onTextChangeListener", "", "rawTextContent", "spanToSpValueMap", "", "Landroid/text/style/AbsoluteSizeSpan;", "textEditBorderView", "Lcom/example/castapp/ui/view/TextEditBorderView;", "windowBackgroundColor", "applyBoldToSelection", "enabled", "applyColorToRange", "start", "end", "color", "applyColorToSelection", "applyFontFamilyToRange", "fontItem", "applyFontFamilyToSelection", "applyFontSizeToRange", "fontSize", "applyFontSizeToSelection", "applyFormatToAllText", "bold", "italic", "applyGlobalLineSpacing", "lineSpacing", "applyItalicToSelection", "applyLetterSpacingToRange", "letterSpacing", "applyLetterSpacingToSelection", "applyLineSpacingToSelection", "applyStrokeToRange", "strokeWidth", "strokeColor", "applyStrokeToSelection", "applyStyleToRange", "style", "applyTextAlignmentToSelection", "alignment", "clearSelectionFormat", "clearStyleFromRange", "createFontSizeSpanWithSpValue", "fontSizeSp", "enterEditMode", "exitEditMode", "extractSpValueFromSpan", "span", "findFontItemByTypeface", "targetTypeface", "Landroid/graphics/Typeface;", "getCurrentFontFamily", "getCurrentFontSize", "getIsInEditMode", "getSelectionColor", "()Ljava/lang/Integer;", "getSelectionFont", "getSelectionFormatState", "Lkotlin/Pair;", "getSelectionFormatStateWithFontSize", "getSelectionLetterSpacing", "getSelectionLineSpacing", "getSelectionStroke", "getTextGravity", "getWindowBackgroundColorState", "handleOverlappingFontSpans", "targetStart", "targetEnd", "hideTextEditBorder", "isSameTypeface", "typeface1", "typeface2", "onSelectionChanged", "selStart", "selEnd", "onTouchEvent", "event", "Landroid/view/MotionEvent;", "performClick", "pixelToSp", "pixels", "removeAllFontSpansFromText", "removeStrokeFromRange", "setBoldEnabled", "setFontFamily", "setFontSize", "size", "setFontSizeSp", "setItalicEnabled", "setOnBorderResizeListener", "listener", "setOnEditStateChangeListener", "setOnSelectionChangeWithAllFormatsListener", "setOnSizeChangeListener", "setOnTextChangeListener", "setTextContent", "content", "setTextGravity", "setWindowBackgroundColor", "setWindowSize", "newWidth", "newHeight", "setupDefaultFont", "setupDefaultStyle", "setupTextWatcher", "showTextEditBorder", "updateFormattedText", "updateTextWindowSize", "app_debug"})
public final class TextWindowView extends androidx.appcompat.widget.AppCompatEditText {
    @org.jetbrains.annotations.NotNull()
    private final android.view.GestureDetector gestureDetector = null;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> onDoubleClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onEditStateChangeListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTextChangeListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.Integer, kotlin.Unit> onSizeChangeListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.Integer, kotlin.Unit> onBorderResizeListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.Boolean, kotlin.Unit> onSelectionChangeListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function3<? super java.lang.Boolean, ? super java.lang.Boolean, ? super java.lang.Integer, kotlin.Unit> onSelectionChangeWithColorListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function4<? super java.lang.Boolean, ? super java.lang.Boolean, ? super java.lang.Integer, ? super kotlin.Triple<java.lang.Boolean, java.lang.Float, java.lang.Integer>, kotlin.Unit> onSelectionChangeWithAllFormatsListener;
    private boolean isBoldEnabled = false;
    private boolean isItalicEnabled = false;
    private int currentFontSizeSp = 13;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.utils.FontPresetManager.FontItem currentFontFamily;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String rawTextContent = "\u9ed8\u8ba4\u6587\u5b57";
    private boolean isInEditMode = false;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.view.TextEditBorderView textEditBorderView;
    private boolean isWindowColorEnabled = false;
    private int windowBackgroundColor = -1;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<android.text.style.AbsoluteSizeSpan, java.lang.Integer> spanToSpValueMap = null;
    
    @kotlin.jvm.JvmOverloads()
    public TextWindowView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs, int defStyleAttr) {
        super(null);
    }
    
    /**
     * 设置默认样式
     */
    private final void setupDefaultStyle() {
    }
    
    /**
     * 设置默认字体
     */
    private final void setupDefaultFont() {
    }
    
    /**
     * 设置文本变化监听器
     */
    private final void setupTextWatcher() {
    }
    
    /**
     * 设置编辑状态变化监听器
     */
    public final void setOnEditStateChangeListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> listener) {
    }
    
    /**
     * 设置文本变化监听器
     */
    public final void setOnTextChangeListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> listener) {
    }
    
    /**
     * 设置尺寸变化监听器
     */
    public final void setOnSizeChangeListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.Integer, kotlin.Unit> listener) {
    }
    
    /**
     * 🎯 设置边框拖动调整大小监听器
     */
    public final void setOnBorderResizeListener(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.Integer, kotlin.Unit> listener) {
    }
    
    /**
     * 设置选择变化监听器（包含所有格式信息）
     */
    public final void setOnSelectionChangeWithAllFormatsListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function4<? super java.lang.Boolean, ? super java.lang.Boolean, ? super java.lang.Integer, ? super kotlin.Triple<java.lang.Boolean, java.lang.Float, java.lang.Integer>, kotlin.Unit> listener) {
    }
    
    /**
     * 进入编辑模式
     */
    public final void enterEditMode() {
    }
    
    /**
     * 退出编辑模式
     */
    public final void exitEditMode() {
    }
    
    /**
     * 显示文本编辑边框
     */
    private final void showTextEditBorder() {
    }
    
    /**
     * 隐藏文本编辑边框
     */
    private final void hideTextEditBorder() {
    }
    
    /**
     * 更新文本窗口尺寸（私有方法，用于编辑模式下的拖动调整）
     */
    private final void updateTextWindowSize(int newWidth, int newHeight) {
    }
    
    /**
     * 设置文本窗口尺寸（公共方法，用于外部设置尺寸）
     */
    public final void setWindowSize(int newWidth, int newHeight) {
    }
    
    /**
     * 设置文本内容
     */
    public final void setTextContent(@org.jetbrains.annotations.NotNull()
    java.lang.String content) {
    }
    
    /**
     * 设置加粗状态（全局格式，向后兼容）
     */
    public final void setBoldEnabled(boolean enabled) {
    }
    
    /**
     * 设置倾斜状态（全局格式，向后兼容）
     */
    public final void setItalicEnabled(boolean enabled) {
    }
    
    /**
     * 设置文本对齐方式
     */
    public final void setTextGravity(int alignment) {
    }
    
    /**
     * 获取当前文本对齐方式
     */
    public final int getTextGravity() {
        return 0;
    }
    
    /**
     * 对选中文字应用对齐方式
     */
    public final void applyTextAlignmentToSelection(int alignment) {
    }
    
    /**
     * 设置字号
     */
    public final void setFontSize(int size) {
    }
    
    /**
     * 设置字号（sp值）
     */
    public final void setFontSizeSp(int fontSizeSp) {
    }
    
    /**
     * 获取当前字号
     */
    public final int getCurrentFontSize() {
        return 0;
    }
    
    /**
     * 获取当前字体族
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.utils.FontPresetManager.FontItem getCurrentFontFamily() {
        return null;
    }
    
    /**
     * 设置字体族
     */
    public final void setFontFamily(@org.jetbrains.annotations.Nullable()
    com.example.castapp.utils.FontPresetManager.FontItem fontItem) {
    }
    
    /**
     * 对选中文字应用字体
     */
    public final void applyFontFamilyToSelection(@org.jetbrains.annotations.Nullable()
    com.example.castapp.utils.FontPresetManager.FontItem fontItem) {
    }
    
    /**
     * 对选中文字应用字号
     */
    public final void applyFontSizeToSelection(int fontSize) {
    }
    
    /**
     * 对选中文字应用字间距
     */
    public final void applyLetterSpacingToSelection(float letterSpacing) {
    }
    
    /**
     * 对选中文字应用行间距
     * 🎯 修复：行间距是TextView级别的属性，始终应用到整个TextView
     */
    public final void applyLineSpacingToSelection(float lineSpacing) {
    }
    
    /**
     * 应用全局行间距
     */
    private final void applyGlobalLineSpacing(float lineSpacing) {
    }
    
    /**
     * 对选中文字应用加粗格式
     */
    public final void applyBoldToSelection(boolean enabled) {
    }
    
    /**
     * 对选中文字应用倾斜格式
     */
    public final void applyItalicToSelection(boolean enabled) {
    }
    
    /**
     * 对选中文字应用颜色
     */
    public final void applyColorToSelection(int color) {
    }
    
    /**
     * 对选中文字应用描边
     */
    public final void applyStrokeToSelection(boolean enabled, float strokeWidth, int strokeColor) {
    }
    
    /**
     * 清除选中文字的所有格式
     */
    public final void clearSelectionFormat() {
    }
    
    /**
     * 📝 应用格式到整个文本（用于布局恢复）
     */
    public final void applyFormatToAllText(boolean bold, boolean italic, int fontSize) {
    }
    
    /**
     * 更新格式化文本显示
     */
    private final void updateFormattedText() {
    }
    
    /**
     * 对指定范围应用样式
     */
    private final void applyStyleToRange(int start, int end, int style, boolean enabled) {
    }
    
    /**
     * 清除指定范围的所有样式
     */
    private final void clearStyleFromRange(int start, int end) {
    }
    
    /**
     * 对指定范围应用颜色
     */
    private final void applyColorToRange(int start, int end, int color) {
    }
    
    /**
     * 对指定范围应用描边
     */
    private final void applyStrokeToRange(int start, int end, float strokeWidth, int strokeColor) {
    }
    
    /**
     * 移除指定范围的描边
     */
    private final void removeStrokeFromRange(int start, int end) {
    }
    
    /**
     * 检测选中文字的格式状态
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.Boolean, java.lang.Boolean> getSelectionFormatState() {
        return null;
    }
    
    /**
     * 检测选中文字的颜色
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getSelectionColor() {
        return null;
    }
    
    /**
     * 检测选中文字的描边状态
     */
    @org.jetbrains.annotations.Nullable()
    public final kotlin.Triple<java.lang.Boolean, java.lang.Float, java.lang.Integer> getSelectionStroke() {
        return null;
    }
    
    /**
     * 检测选中文字的字体
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.utils.FontPresetManager.FontItem getSelectionFont() {
        return null;
    }
    
    /**
     * 检测选中文字的字间距
     */
    public final float getSelectionLetterSpacing() {
        return 0.0F;
    }
    
    /**
     * 检测选中文字的行间距（TextView级别属性）
     */
    public final float getSelectionLineSpacing() {
        return 0.0F;
    }
    
    /**
     * 根据Typeface查找对应的FontItem
     */
    private final com.example.castapp.utils.FontPresetManager.FontItem findFontItemByTypeface(android.graphics.Typeface targetTypeface) {
        return null;
    }
    
    /**
     * 比较两个Typeface是否相同
     */
    private final boolean isSameTypeface(android.graphics.Typeface typeface1, android.graphics.Typeface typeface2) {
        return false;
    }
    
    /**
     * 检测选中文字的格式状态（包含字号）
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Triple<java.lang.Boolean, java.lang.Boolean, java.lang.Integer> getSelectionFormatStateWithFontSize() {
        return null;
    }
    
    /**
     * 对指定范围应用字号
     */
    private final void applyFontSizeToRange(int start, int end, int fontSize) {
    }
    
    /**
     * 对指定范围应用字间距
     * 🎯 修复：改进字间距应用逻辑，避免影响其他格式
     */
    private final void applyLetterSpacingToRange(int start, int end, float letterSpacing) {
    }
    
    /**
     * 对指定范围应用字体
     * 🎯 修复：正确处理字体Span重叠问题，避免意外移除相邻字符的字体设置
     */
    private final void applyFontFamilyToRange(int start, int end, com.example.castapp.utils.FontPresetManager.FontItem fontItem) {
    }
    
    /**
     * 🎯 智能处理重叠的字体Span，避免误删相邻字符的字体设置
     * 核心思路：对于部分重叠的Span，进行分割处理，只移除目标范围内的部分
     */
    private final void handleOverlappingFontSpans(int targetStart, int targetEnd) {
    }
    
    /**
     * 移除文本中所有的字体相关Span
     */
    private final void removeAllFontSpansFromText() {
    }
    
    /**
     * 创建带有sp值标记的字号Span
     */
    private final android.text.style.AbsoluteSizeSpan createFontSizeSpanWithSpValue(int fontSizeSp) {
        return null;
    }
    
    /**
     * 从Span中提取原始sp值
     */
    private final int extractSpValueFromSpan(android.text.style.AbsoluteSizeSpan span) {
        return 0;
    }
    
    /**
     * 将像素值转换为sp值（后备方案）
     */
    private final int pixelToSp(int pixels) {
        return 0;
    }
    
    @java.lang.Override()
    protected void onSelectionChanged(int selStart, int selEnd) {
    }
    
    @java.lang.Override()
    public boolean onTouchEvent(@org.jetbrains.annotations.NotNull()
    android.view.MotionEvent event) {
        return false;
    }
    
    @java.lang.Override()
    public boolean performClick() {
        return false;
    }
    
    /**
     * 设置窗口背景颜色
     */
    public final void setWindowBackgroundColor(boolean enabled, int color) {
    }
    
    /**
     * 获取当前窗口背景颜色状态
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.Boolean, java.lang.Integer> getWindowBackgroundColorState() {
        return null;
    }
    
    /**
     * 获取当前编辑模式状态
     */
    public final boolean getIsInEditMode() {
        return false;
    }
    
    @kotlin.jvm.JvmOverloads()
    public TextWindowView(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads()
    public TextWindowView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs) {
        super(null);
    }
}