package com.example.castapp.ui.view

import com.example.castapp.model.WindowVisualizationData
import com.example.castapp.ui.windowsettings.TransformManager
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * 🧪 文字窗口缩放功能测试
 * 验证遥控端文字窗口缩放功能的正确性
 */
class WindowScalingTest {

    private lateinit var testWindowData: WindowVisualizationData

    @Before
    fun setup() {
        // 创建测试用的文字窗口数据
        testWindowData = WindowVisualizationData(
            connectionId = "text_test_window",
            visualizedX = 100f,
            visualizedY = 100f,
            visualizedWidth = 200f,
            visualizedHeight = 150f,
            scaleFactor = 1.0f,
            rotationAngle = 0f,
            zOrder = 1,
            textContent = "测试文字内容"
        )
    }

    @Test
    fun `测试缩放范围限制一致性`() {
        // 验证缩放范围与TransformManager一致
        val minScale = TransformManager.MIN_SCALE_FACTOR
        val maxScale = TransformManager.MAX_SCALE_FACTOR
        
        assertEquals(0.2f, minScale, "最小缩放因子应为0.2")
        assertEquals(8.0f, maxScale, "最大缩放因子应为8.0")
        
        // 测试边界值
        val testScales = listOf(0.1f, 0.2f, 1.0f, 8.0f, 10.0f)
        val expectedResults = listOf(0.2f, 0.2f, 1.0f, 8.0f, 8.0f)
        
        testScales.forEachIndexed { index, scale ->
            val clampedScale = scale.coerceIn(minScale, maxScale)
            assertEquals(expectedResults[index], clampedScale, 
                "缩放因子 $scale 应被限制为 ${expectedResults[index]}")
        }
    }

    @Test
    fun `测试文字窗口识别`() {
        // 测试文字窗口ID识别
        assertTrue(testWindowData.connectionId.startsWith("text_"), 
            "文字窗口ID应以'text_'开头")
        
        // 测试普通窗口ID
        val normalWindowData = testWindowData.copy(connectionId = "normal_window")
        assertFalse(normalWindowData.connectionId.startsWith("text_"), 
            "普通窗口ID不应以'text_'开头")
    }

    @Test
    fun `测试缩放状态数据模型`() {
        // 测试缩放因子的数据模型正确性
        val scaledWindow = testWindowData.copy(scaleFactor = 2.0f)
        assertEquals(2.0f, scaledWindow.scaleFactor, "缩放因子应正确设置")

        // 测试其他属性保持不变
        assertEquals(testWindowData.connectionId, scaledWindow.connectionId, "连接ID应保持不变")
        assertEquals(testWindowData.visualizedX, scaledWindow.visualizedX, "X坐标应保持不变")
        assertEquals(testWindowData.visualizedY, scaledWindow.visualizedY, "Y坐标应保持不变")
    }

    @Test
    fun `测试文字窗口缩放中心点`() {
        // 文字窗口应使用左上角作为缩放中心点
        val textWindow = testWindowData
        val originalX = textWindow.visualizedX
        val originalY = textWindow.visualizedY
        
        // 对于文字窗口，缩放后位置应保持不变（左上角锚点）
        assertEquals(originalX, textWindow.visualizedX, "文字窗口缩放后X坐标应保持不变")
        assertEquals(originalY, textWindow.visualizedY, "文字窗口缩放后Y坐标应保持不变")
    }

    @Test
    fun `测试普通窗口缩放中心点`() {
        // 普通窗口应使用中心点作为缩放中心点
        val normalWindow = testWindowData.copy(connectionId = "normal_window")
        val centerX = normalWindow.visualizedX + normalWindow.visualizedWidth / 2
        val centerY = normalWindow.visualizedY + normalWindow.visualizedHeight / 2
        
        // 验证中心点计算正确
        assertEquals(200f, centerX, "普通窗口中心点X坐标计算正确")
        assertEquals(175f, centerY, "普通窗口中心点Y坐标计算正确")
    }

    @Test
    fun `测试缩放因子计算`() {
        // 测试相对缩放因子转换为绝对缩放因子
        val baseScale = 1.5f
        val relativeScale = 2.0f
        val expectedAbsoluteScale = baseScale * relativeScale
        
        assertEquals(3.0f, expectedAbsoluteScale, "绝对缩放因子计算正确")
        
        // 测试边界情况
        val minRelativeScale = TransformManager.MIN_SCALE_FACTOR / baseScale
        val maxRelativeScale = TransformManager.MAX_SCALE_FACTOR / baseScale
        
        assertTrue(minRelativeScale > 0, "最小相对缩放因子应大于0")
        assertTrue(maxRelativeScale > 0, "最大相对缩放因子应大于0")
    }

    @Test
    fun `测试缩放数据一致性`() {
        // 测试缩放数据的一致性验证
        val scaleFactors = listOf(0.5f, 1.0f, 1.5f, 2.0f, 3.0f)

        scaleFactors.forEach { scale ->
            val scaledWindow = testWindowData.copy(scaleFactor = scale)
            assertEquals(scale, scaledWindow.scaleFactor, "缩放因子应正确保存")

            // 验证其他属性不受影响
            assertEquals(testWindowData.connectionId, scaledWindow.connectionId)
            assertEquals(testWindowData.visualizedWidth, scaledWindow.visualizedWidth)
            assertEquals(testWindowData.visualizedHeight, scaledWindow.visualizedHeight)
        }
    }
}
