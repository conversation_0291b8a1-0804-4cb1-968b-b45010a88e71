package com.example.castapp.ui.windowsettings;

/**
 * 遥控端文字窗口管理器
 * 专门为遥控端文字窗口编辑功能设计，复用现有的TextEditPanel和TextWindowView
 *
 * 🎯 优化说明：
 * - 直接使用WindowVisualizationContainerView中已有的TextWindowView
 * - 移除了历史遗留的TextView查找和替换逻辑
 * - 通过属性控制实现显示/编辑模式切换，无需控件替换
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u008a\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0016\n\u0002\u0010$\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u001c\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u00002\u00020\u0001B1\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\u0002\u0010\u000bJ\u0010\u0010%\u001a\u00020&2\u0006\u0010\'\u001a\u00020\u000fH\u0002J\u0010\u0010(\u001a\u00020&2\u0006\u0010)\u001a\u00020\rH\u0002J\u0010\u0010*\u001a\u00020&2\u0006\u0010+\u001a\u00020\u000fH\u0002J\u0018\u0010,\u001a\u00020&2\u0006\u0010-\u001a\u00020\u00152\u0006\u0010.\u001a\u00020\u0015H\u0002J\u0010\u0010/\u001a\u00020&2\u0006\u00100\u001a\u00020\u0011H\u0002J\u0010\u00101\u001a\u00020&2\u0006\u00102\u001a\u00020\u0011H\u0002J \u00103\u001a\u00020&2\u0006\u00104\u001a\u00020\u00152\u0006\u00105\u001a\u00020\u00112\u0006\u00106\u001a\u00020\u000fH\u0002J\u0010\u00107\u001a\u00020&2\u0006\u00108\u001a\u00020\u000fH\u0002J\u0018\u00109\u001a\u00020&2\u0006\u00104\u001a\u00020\u00152\u0006\u0010\'\u001a\u00020\u000fH\u0002J\b\u0010:\u001a\u00020&H\u0002J\b\u0010;\u001a\u00020&H\u0002J\u0014\u0010<\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010=H\u0002J\u0014\u0010>\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010=H\u0002J\b\u0010?\u001a\u00020&H\u0002J\b\u0010@\u001a\u00020&H\u0002J\u0010\u0010A\u001a\u00020\u00112\u0006\u0010B\u001a\u00020\u0011H\u0002J\b\u0010C\u001a\u00020&H\u0002J\b\u0010D\u001a\u00020&H\u0002J\b\u0010E\u001a\u00020\u0015H\u0002J\u0012\u0010F\u001a\u0004\u0018\u00010\u001c2\u0006\u0010G\u001a\u00020HH\u0002J\u001a\u0010I\u001a\u0004\u0018\u00010 2\u0006\u0010G\u001a\u00020\n2\u0006\u0010J\u001a\u00020\u0005H\u0002J\b\u0010K\u001a\u00020&H\u0002J\n\u0010L\u001a\u0004\u0018\u00010\rH\u0002J\b\u0010M\u001a\u00020\u0011H\u0002J\b\u0010N\u001a\u00020\u0011H\u0002J\n\u0010O\u001a\u0004\u0018\u00010\u001aH\u0002J\u0006\u0010P\u001a\u00020&J\u000e\u0010Q\u001a\u00020&2\u0006\u0010R\u001a\u00020\u0015J\b\u0010S\u001a\u00020\u0015H\u0002J\b\u0010T\u001a\u00020&H\u0002J\u0018\u0010U\u001a\u00020&2\u0006\u0010V\u001a\u00020\u00112\u0006\u0010W\u001a\u00020\u0011H\u0002J\u0010\u0010X\u001a\u00020&2\u0006\u00102\u001a\u00020\u0011H\u0002J\u001c\u0010Y\u001a\u00020&2\u0012\u0010Z\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010=H\u0002J\u0014\u0010[\u001a\u00020&2\f\u0010\\\u001a\b\u0012\u0004\u0012\u00020\u00150\u001eJ\b\u0010]\u001a\u00020&H\u0002J\u0010\u0010^\u001a\u00020&2\u0006\u0010_\u001a\u00020\u001cH\u0002J\u0006\u0010`\u001a\u00020&J\b\u0010a\u001a\u00020&H\u0002J\u0010\u0010b\u001a\u00020&2\u0006\u0010G\u001a\u00020 H\u0002J\u0018\u0010c\u001a\u00020&2\u0006\u0010G\u001a\u00020 2\u0006\u0010d\u001a\u00020eH\u0002J\u0018\u0010f\u001a\u00020&2\u0006\u0010g\u001a\u00020\u000f2\u0006\u0010h\u001a\u00020\u000fH\u0002J$\u0010i\u001a\u00020&2\u0006\u0010j\u001a\u00020\u00052\u0012\u0010Z\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010=H\u0002J \u0010k\u001a\u00020&2\u0006\u0010G\u001a\u00020 2\u0006\u0010l\u001a\u00020\u00112\u0006\u0010m\u001a\u00020\u0011H\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0018\u001a\n\u0012\u0004\u0012\u00020\u001a\u0018\u00010\u0019X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001b\u001a\u0004\u0018\u00010\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u001d\u001a\n\u0012\u0004\u0012\u00020\u0015\u0018\u00010\u001eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001f\u001a\u0004\u0018\u00010 X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010!\u001a\u0004\u0018\u00010\"X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020$X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006n"}, d2 = {"Lcom/example/castapp/ui/windowsettings/RemoteTextWindowManager;", "", "context", "Landroid/content/Context;", "textId", "", "initialTextContent", "remoteReceiverConnection", "Lcom/example/castapp/model/RemoteReceiverConnection;", "windowVisualizationView", "Lcom/example/castapp/ui/view/WindowContainerVisualizationView;", "(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;Lcom/example/castapp/model/RemoteReceiverConnection;Lcom/example/castapp/ui/view/WindowContainerVisualizationView;)V", "currentFontFamily", "Lcom/example/castapp/utils/FontPresetManager$FontItem;", "currentFontSize", "", "currentLetterSpacing", "", "currentLineSpacing", "currentTextContent", "isBoldEnabled", "", "isHidingEditPanel", "isItalicEnabled", "mainContainerRef", "Ljava/lang/ref/WeakReference;", "Landroid/widget/FrameLayout;", "remoteTextWindowView", "Lcom/example/castapp/ui/view/TextWindowView;", "syncCallback", "Lkotlin/Function0;", "targetWindowContainerView", "Lcom/example/castapp/ui/view/WindowVisualizationContainerView;", "textEditPanel", "Lcom/example/castapp/ui/view/TextEditPanel;", "textFormatManager", "Lcom/example/castapp/utils/TextFormatManager;", "applyColorChangeRealTime", "", "color", "applyFontFamilyChangeRealTime", "fontItem", "applyFontSizeChangeRealTime", "fontSize", "applyFormatChangesRealTime", "bold", "italic", "applyLetterSpacingChangeRealTime", "letterSpacing", "applyLineSpacingChangeRealTime", "lineSpacing", "applyStrokeChangeRealTime", "enabled", "strokeWidth", "strokeColor", "applyTextAlignmentChangeRealTime", "alignment", "applyWindowColorChangeRealTime", "checkAndSyncFormatData", "clearSelectionFormat", "collectCurrentFormatData", "", "collectWindowTransformData", "disableBorderResizing", "disableTextViewEditing", "dpToPx", "dp", "enableBorderResizing", "enableTextViewEditing", "findRemoteTextWindowView", "findTextWindowViewInContainer", "containerView", "Landroid/view/ViewGroup;", "findWindowVisualizationContainerView", "targetConnectionId", "forceSyncFormatData", "getCurrentFontFamily", "getCurrentLetterSpacing", "getCurrentLineSpacing", "getMainContainer", "hideEditPanel", "hideEditPanelWithSync", "shouldSync", "isSyncEnabled", "notifyWindowContainerDragEnd", "performWindowContainerDrag", "deltaX", "deltaY", "saveLineSpacingToPreferences", "sendFormatSyncMessage", "formatData", "setSyncCallback", "callback", "setupEditPanelListeners", "setupTextWindowViewListeners", "textWindowView", "showEditPanel", "syncFormatStateFromTextView", "updateContainerBorderView", "updateExistingBorderViewSize", "windowData", "Lcom/example/castapp/model/WindowVisualizationData;", "updateRemoteTextWindowSize", "newWidth", "newHeight", "updateUnifiedConfigTextContent", "newTextContent", "updateWindowBorderTranslation", "translationX", "translationY", "app_debug"})
public final class RemoteTextWindowManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String textId = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String initialTextContent = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.model.RemoteReceiverConnection remoteReceiverConnection = null;
    @org.jetbrains.annotations.Nullable()
    private final com.example.castapp.ui.view.WindowContainerVisualizationView windowVisualizationView = null;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.view.WindowVisualizationContainerView targetWindowContainerView;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.view.TextWindowView remoteTextWindowView;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.view.TextEditPanel textEditPanel;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.utils.TextFormatManager textFormatManager = null;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String currentTextContent;
    private boolean isBoldEnabled = false;
    private boolean isItalicEnabled = false;
    private int currentFontSize = 13;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.utils.FontPresetManager.FontItem currentFontFamily;
    private float currentLetterSpacing = 0.0F;
    private float currentLineSpacing = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private java.lang.ref.WeakReference<android.widget.FrameLayout> mainContainerRef;
    private boolean isHidingEditPanel = false;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<java.lang.Boolean> syncCallback;
    
    public RemoteTextWindowManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String textId, @org.jetbrains.annotations.NotNull()
    java.lang.String initialTextContent, @org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection remoteReceiverConnection, @org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.view.WindowContainerVisualizationView windowVisualizationView) {
        super();
    }
    
    /**
     * 🎯 设置同步回调函数
     */
    public final void setSyncCallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<java.lang.Boolean> callback) {
    }
    
    /**
     * 显示编辑面板
     */
    public final void showEditPanel() {
    }
    
    /**
     * 隐藏编辑面板（带同步控制）
     */
    public final void hideEditPanelWithSync(boolean shouldSync) {
    }
    
    /**
     * 隐藏编辑面板（默认行为，检查同步开关状态）
     */
    public final void hideEditPanel() {
    }
    
    /**
     * 让TextWindowView退出可编辑状态
     */
    private final void disableTextViewEditing() {
    }
    
    /**
     * 查找遥控端文字窗口视图
     */
    private final boolean findRemoteTextWindowView() {
        return false;
    }
    
    /**
     * 在WindowContainerVisualizationView中查找WindowVisualizationContainerView
     */
    private final com.example.castapp.ui.view.WindowVisualizationContainerView findWindowVisualizationContainerView(com.example.castapp.ui.view.WindowContainerVisualizationView containerView, java.lang.String targetConnectionId) {
        return null;
    }
    
    /**
     * 在容器中查找TextWindowView
     */
    private final com.example.castapp.ui.view.TextWindowView findTextWindowViewInContainer(android.view.ViewGroup containerView) {
        return null;
    }
    
    /**
     * 为TextWindowView设置监听器
     */
    private final void setupTextWindowViewListeners(com.example.castapp.ui.view.TextWindowView textWindowView) {
    }
    
    /**
     * 让TextWindowView进入可编辑状态
     */
    private final void enableTextViewEditing() {
    }
    
    /**
     * 设置编辑面板监听器
     */
    private final void setupEditPanelListeners() {
    }
    
    /**
     * 实时应用格式变化
     */
    private final void applyFormatChangesRealTime(boolean bold, boolean italic) {
    }
    
    /**
     * 实时应用字号变化
     */
    private final void applyFontSizeChangeRealTime(int fontSize) {
    }
    
    /**
     * 实时应用字体变化
     */
    private final void applyFontFamilyChangeRealTime(com.example.castapp.utils.FontPresetManager.FontItem fontItem) {
    }
    
    /**
     * 实时应用字间距变化
     */
    private final void applyLetterSpacingChangeRealTime(float letterSpacing) {
    }
    
    /**
     * 实时应用行间距变化
     */
    private final void applyLineSpacingChangeRealTime(float lineSpacing) {
    }
    
    /**
     * 实时应用文本对齐变化
     */
    private final void applyTextAlignmentChangeRealTime(int alignment) {
    }
    
    /**
     * 🎯 关键修复：实时应用字色变化
     */
    private final void applyColorChangeRealTime(int color) {
    }
    
    /**
     * 🎯 关键修复：实时应用描边变化
     */
    private final void applyStrokeChangeRealTime(boolean enabled, float strokeWidth, int strokeColor) {
    }
    
    /**
     * 🎯 关键修复：实时应用窗色变化
     */
    private final void applyWindowColorChangeRealTime(boolean enabled, int color) {
    }
    
    /**
     * 强制同步格式数据（不检查同步开关状态）
     */
    private final void forceSyncFormatData() {
    }
    
    /**
     * 检查并同步格式数据（根据同步开关状态）
     */
    private final void checkAndSyncFormatData() {
    }
    
    /**
     * 🎯 从TextWindowView同步格式状态到管理器
     */
    private final void syncFormatStateFromTextView() {
    }
    
    /**
     * 获取当前行间距
     */
    private final float getCurrentLineSpacing() {
        return 0.0F;
    }
    
    /**
     * 获取当前字体
     */
    private final com.example.castapp.utils.FontPresetManager.FontItem getCurrentFontFamily() {
        return null;
    }
    
    /**
     * 获取当前字间距
     */
    private final float getCurrentLetterSpacing() {
        return 0.0F;
    }
    
    /**
     * 保存行距到SharedPreferences
     */
    private final void saveLineSpacingToPreferences(float lineSpacing) {
    }
    
    /**
     * 🎯 关键修复：执行窗口容器拖动（参考接收端实现）
     */
    private final void performWindowContainerDrag(float deltaX, float deltaY) {
    }
    
    /**
     * 🎯 关键修复：通知窗口容器拖动结束
     */
    private final void notifyWindowContainerDragEnd() {
    }
    
    /**
     * 🎯 关键修复：更新文字窗口边框变换（参考接收端实现）
     */
    private final void updateWindowBorderTranslation(com.example.castapp.ui.view.WindowVisualizationContainerView containerView, float translationX, float translationY) {
    }
    
    /**
     * 🎯 关键修复：清除选中文字的格式
     */
    private final void clearSelectionFormat() {
    }
    
    /**
     * 检查同步开关状态
     */
    private final boolean isSyncEnabled() {
        return false;
    }
    
    /**
     * 🎯 启用边框拖动调整大小功能
     */
    private final void enableBorderResizing() {
    }
    
    /**
     * 🎯 禁用边框拖动调整大小功能
     */
    private final void disableBorderResizing() {
    }
    
    /**
     * 🎯 更新遥控端文字窗口尺寸
     */
    private final void updateRemoteTextWindowSize(int newWidth, int newHeight) {
    }
    
    /**
     * 🎯 新增：更新容器边框View尺寸（平滑更新，避免闪烁）
     */
    private final void updateContainerBorderView(com.example.castapp.ui.view.WindowVisualizationContainerView containerView) {
    }
    
    /**
     * 🎯 新增：平滑更新现有边框View的尺寸（避免重新创建导致的闪烁）
     */
    private final void updateExistingBorderViewSize(com.example.castapp.ui.view.WindowVisualizationContainerView containerView, com.example.castapp.model.WindowVisualizationData windowData) {
    }
    
    /**
     * 🎯 工具方法：dp转px
     */
    private final float dpToPx(float dp) {
        return 0.0F;
    }
    
    /**
     * 收集当前格式数据
     */
    private final java.util.Map<java.lang.String, java.lang.Object> collectCurrentFormatData() {
        return null;
    }
    
    /**
     * 🎯 收集窗口变换数据
     */
    private final java.util.Map<java.lang.String, java.lang.Object> collectWindowTransformData() {
        return null;
    }
    
    /**
     * 发送格式同步消息
     */
    private final void sendFormatSyncMessage(java.util.Map<java.lang.String, ? extends java.lang.Object> formatData) {
    }
    
    /**
     * 🎯 关键修复：更新统一配置管理器中的文字内容
     */
    private final void updateUnifiedConfigTextContent(java.lang.String newTextContent, java.util.Map<java.lang.String, ? extends java.lang.Object> formatData) {
    }
    
    /**
     * 获取主容器（用于显示编辑面板）
     */
    private final android.widget.FrameLayout getMainContainer() {
        return null;
    }
}