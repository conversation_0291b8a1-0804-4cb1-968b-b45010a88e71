# 编辑面板关闭同步逻辑一致性修复

## 问题描述

在修复了遥控端文字窗口编辑开关独立性问题后，发现了一个新的问题：

**现象**：
1. **通过编辑开关关闭**：退出编辑模式时，系统会将编辑内容同步到接收端
2. **通过格式面板右上角关闭图标**：退出编辑模式时，系统不会将编辑内容同步到接收端

**期望行为**：
无论通过哪种方式退出编辑模式，都应该根据同步开关状态一致地处理内容同步。

## 问题根因分析

通过代码分析发现，两种退出方式的同步逻辑处理不一致：

### 1. 编辑开关关闭的处理流程
```kotlin
// RemoteWindowManagerDialog.handleTextWindowEditModeToggle()
if (!isEnabled) {
    hideRemoteTextWindowEditPanelWithSync(connectionId, isSyncEnabled)
}
```
- 直接根据同步开关状态决定是否同步
- 调用带同步控制的隐藏方法

### 2. 格式面板关闭图标的处理流程
```kotlin
// RemoteTextWindowManager.setupEditPanelListeners()
textEditPanel?.setOnCloseListener {
    hideEditPanel() // 调用默认的隐藏方法
}

// RemoteTextWindowManager.hideEditPanel()
fun hideEditPanel() {
    // ...
    checkAndSyncFormatData() // 检查同步开关状态
}
```
- 使用默认的隐藏方法
- 内部检查同步开关状态决定是否同步

### 3. 逻辑不一致的原因

虽然两种方式最终都会检查同步开关状态，但是：
1. **编辑开关关闭**：在 `RemoteWindowManagerDialog` 层面检查同步开关状态
2. **格式面板关闭图标**：在 `RemoteTextWindowManager` 层面检查同步开关状态

这种不一致可能导致在某些情况下行为差异。

## 修复方案

### 核心思路
统一两种退出方式的同步逻辑处理，确保行为完全一致：

1. **创建统一的同步控制方法**：`hideEditPanelWithSync(shouldSync: Boolean)`
2. **修改格式面板关闭监听器**：使其也根据同步开关状态决定是否同步
3. **保持编辑开关关闭的现有逻辑**：继续使用带同步控制的方法

### 具体修改

#### 1. 创建带同步控制的隐藏方法
**文件**：`app/src/main/java/com/example/castapp/ui/windowsettings/RemoteTextWindowManager.kt`

**新增方法**：
```kotlin
/**
 * 隐藏编辑面板（带同步控制）
 */
fun hideEditPanelWithSync(shouldSync: Boolean) {
    try {
        // 让TextView退出编辑状态
        disableTextViewEditing()
        
        // 隐藏编辑面板
        textEditPanel?.hide()
        
        // 根据参数决定是否同步格式数据
        if (shouldSync) {
            // 强制同步，不检查同步开关状态
            forceSyncFormatData()
        }
    } catch (e: Exception) {
        AppLog.e("【遥控端文字窗口管理器】隐藏编辑面板失败", e)
    }
}
```

#### 2. 创建强制同步方法
**新增方法**：
```kotlin
/**
 * 强制同步格式数据（不检查同步开关状态）
 */
private fun forceSyncFormatData() {
    try {
        // 获取当前格式数据
        val formatData = collectCurrentFormatData()
        
        // 发送同步消息到接收端
        sendFormatSyncMessage(formatData)
    } catch (e: Exception) {
        AppLog.e("【遥控端文字窗口管理器】强制同步格式数据失败", e)
    }
}
```

#### 3. 修改格式面板关闭监听器
**修改前**：
```kotlin
textEditPanel?.setOnCloseListener {
    AppLog.d("【遥控端文字窗口管理器】用户关闭编辑面板")
    hideEditPanel()
}
```

**修改后**：
```kotlin
textEditPanel?.setOnCloseListener {
    AppLog.d("【遥控端文字窗口管理器】用户关闭编辑面板")
    // 🎯 关键修复：根据同步开关状态决定是否同步，与编辑开关关闭行为保持一致
    val shouldSync = isSyncEnabled()
    hideEditPanelWithSync(shouldSync)
}
```

#### 4. 修改编辑开关关闭处理
**文件**：`app/src/main/java/com/example/castapp/ui/dialog/RemoteWindowManagerDialog.kt`

**修改前**：
```kotlin
} else {
    hideRemoteTextWindowEditPanel(connectionId)
    
    if (isSyncEnabled) {
        syncTextContentToReceiver(connectionId)
    }
}
```

**修改后**：
```kotlin
} else {
    // 🎯 修复：关闭编辑模式时，隐藏编辑面板并根据同步开关决定是否同步
    hideRemoteTextWindowEditPanelWithSync(connectionId, isSyncEnabled)
}
```

#### 5. 创建统一的隐藏方法
**新增方法**：
```kotlin
/**
 * 📝 隐藏遥控端文字窗口编辑面板（带同步控制）
 */
private fun hideRemoteTextWindowEditPanelWithSync(connectionId: String, shouldSync: Boolean) {
    try {
        val remoteTextWindowManager = activeRemoteTextWindowManagers[connectionId]
        if (remoteTextWindowManager != null) {
            // 隐藏编辑面板并根据参数决定是否同步
            remoteTextWindowManager.hideEditPanelWithSync(shouldSync)
            
            // 从活跃管理器集合中移除
            activeRemoteTextWindowManagers.remove(connectionId)
        }
    } catch (e: Exception) {
        AppLog.e("【远程窗口管理】隐藏编辑面板失败", e)
    }
}
```

## 修复效果

修复后的行为：

1. **编辑开关关闭**：
   - 调用 `hideRemoteTextWindowEditPanelWithSync(connectionId, isSyncEnabled)`
   - 根据同步开关状态决定是否同步

2. **格式面板关闭图标**：
   - 调用 `hideEditPanelWithSync(isSyncEnabled())`
   - 同样根据同步开关状态决定是否同步

3. **行为完全一致**：
   - 两种退出方式都使用相同的同步逻辑
   - 都根据同步开关状态一致地处理内容同步

## 测试验证

修复后应该能够实现以下行为：
- [ ] 同步开关开启时，无论通过哪种方式退出编辑模式，都会同步内容到接收端
- [ ] 同步开关关闭时，无论通过哪种方式退出编辑模式，都不会同步内容到接收端
- [ ] 两种退出方式的同步行为完全一致

## 相关文件

修改的文件：
- `app/src/main/java/com/example/castapp/ui/dialog/RemoteWindowManagerDialog.kt`
- `app/src/main/java/com/example/castapp/ui/windowsettings/RemoteTextWindowManager.kt`

涉及的方法：
- `RemoteWindowManagerDialog.handleTextWindowEditModeToggle()`
- `RemoteWindowManagerDialog.hideRemoteTextWindowEditPanelWithSync()`
- `RemoteTextWindowManager.hideEditPanelWithSync()`
- `RemoteTextWindowManager.forceSyncFormatData()`
