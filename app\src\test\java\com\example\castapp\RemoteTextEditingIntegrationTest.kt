package com.example.castapp

import android.content.Context
import com.example.castapp.model.RemoteReceiverConnection
import com.example.castapp.websocket.ControlMessage
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment

/**
 * 遥控端文字编辑功能集成测试
 * 测试从编辑模式触发到格式同步的完整流程
 */
@RunWith(RobolectricTestRunner::class)
class RemoteTextEditingIntegrationTest {

    private lateinit var context: Context
    private lateinit var remoteReceiverConnection: RemoteReceiverConnection

    @Before
    fun setUp() {
        context = RuntimeEnvironment.getApplication()
        
        remoteReceiverConnection = RemoteReceiverConnection(
            id = "integration_test_receiver",
            ipAddress = "*************",
            port = 7777,
            deviceName = "集成测试接收端",
            isConnected = true
        )
    }

    @Test
    fun testCompleteEditingWorkflow() {
        println("🚀 开始遥控端文字编辑功能集成测试")
        
        try {
            // 1. 测试创建遥控端文字窗口管理器
            val remoteTextWindowManager = com.example.castapp.ui.windowsettings.RemoteTextWindowManager(
                context = context,
                textId = "text_integration_test",
                initialTextContent = "集成测试文字内容",
                remoteReceiverConnection = remoteReceiverConnection
            )
            println("✅ 步骤1: 遥控端文字窗口管理器创建成功")

            // 2. 测试显示编辑面板
            remoteTextWindowManager.showEditPanel()
            println("✅ 步骤2: 编辑面板显示成功")

            // 3. 模拟格式变更（通过隐藏编辑面板触发格式同步）
            remoteTextWindowManager.hideEditPanel()
            println("✅ 步骤3: 编辑面板隐藏并触发格式同步")

            println("🎉 遥控端文字编辑功能集成测试完成")

        } catch (e: Exception) {
            println("❌ 集成测试失败: ${e.message}")
            e.printStackTrace()
            throw e
        }
    }

    @Test
    fun testControlMessageCreation() {
        println("🔗 测试文字格式同步消息创建")
        
        try {
            // 测试创建文字格式同步消息
            val formatData = mapOf(
                "connectionId" to "text_test_001",
                "textContent" to "测试文字内容",
                "isBold" to true,
                "isItalic" to false,
                "fontSize" to 16
            )
            
            val syncMessage = ControlMessage.createTextFormatSyncMessage("test_connection", formatData)
            
            // 验证消息类型
            assert(syncMessage.type == ControlMessage.TYPE_TEXT_FORMAT_SYNC)
            assert(syncMessage.connectionId == "test_connection")
            assert(syncMessage.data["format_data"] == formatData)
            assert(syncMessage.data.containsKey("timestamp"))
            
            println("✅ 文字格式同步消息创建测试通过")
            
        } catch (e: Exception) {
            println("❌ 文字格式同步消息创建测试失败: ${e.message}")
            throw e
        }
    }

    @Test
    fun testFormatDataStructure() {
        println("📋 测试格式数据结构")
        
        try {
            // 测试格式数据结构的完整性
            val formatData = mapOf(
                "connectionId" to "text_test_001",
                "textContent" to "测试文字内容",
                "isBold" to true,
                "isItalic" to false,
                "fontSize" to 16,
                "fontName" to "Roboto",
                "fontFamily" to "sans-serif",
                "lineSpacing" to 1.5f,
                "textAlignment" to android.view.Gravity.CENTER,
                "richTextData" to "mock_rich_text_data"
            )
            
            // 验证必需字段
            assert(formatData.containsKey("connectionId"))
            assert(formatData.containsKey("textContent"))
            assert(formatData.containsKey("isBold"))
            assert(formatData.containsKey("isItalic"))
            assert(formatData.containsKey("fontSize"))
            
            // 验证扩展字段
            assert(formatData.containsKey("fontName"))
            assert(formatData.containsKey("lineSpacing"))
            assert(formatData.containsKey("textAlignment"))
            
            println("✅ 格式数据结构测试通过")
            
        } catch (e: Exception) {
            println("❌ 格式数据结构测试失败: ${e.message}")
            throw e
        }
    }

    @Test
    fun testRemoteReceiverConnectionValidation() {
        println("🔗 测试远程接收端连接验证")
        
        try {
            // 验证连接参数
            assert(remoteReceiverConnection.id.isNotEmpty())
            assert(remoteReceiverConnection.ipAddress.isNotEmpty())
            assert(remoteReceiverConnection.port > 0)
            assert(remoteReceiverConnection.deviceName.isNotEmpty())
            
            // 验证连接状态
            assert(remoteReceiverConnection.isConnected)
            
            println("✅ 远程接收端连接验证测试通过")
            
        } catch (e: Exception) {
            println("❌ 远程接收端连接验证测试失败: ${e.message}")
            throw e
        }
    }

    @Test
    fun testErrorHandling() {
        println("⚠️ 测试错误处理")
        
        try {
            // 测试无效连接ID的情况
            val invalidConnection = RemoteReceiverConnection(
                id = "",
                ipAddress = "invalid_ip",
                port = -1,
                deviceName = "",
                isConnected = false
            )
            
            // 创建管理器时应该能处理无效连接
            val remoteTextWindowManager = com.example.castapp.ui.windowsettings.RemoteTextWindowManager(
                context = context,
                textId = "text_error_test",
                initialTextContent = "错误测试文字",
                remoteReceiverConnection = invalidConnection
            )
            
            // 尝试显示编辑面板（应该能优雅处理错误）
            remoteTextWindowManager.showEditPanel()
            
            println("✅ 错误处理测试通过")
            
        } catch (e: Exception) {
            println("⚠️ 错误处理测试中捕获到预期异常: ${e.message}")
            // 这是预期的行为，不应该导致测试失败
        }
    }
}
