package com.example.castapp

import org.junit.Test
import org.junit.Assert.*

/**
 * 🎯 文字窗口旋转位置同步修复测试
 * 验证修复后文字窗口在遥控端的位置同步是否准确
 */
class TextWindowRotationSyncTest {

    @Test
    fun testTextWindowIdentification() {
        // 🎯 测试文字窗口识别逻辑
        val textConnectionId = "text_test_window_001"
        val nonTextConnectionId = "cast_window_001"

        val isTextWindow1 = textConnectionId.startsWith("text_")
        val isTextWindow2 = nonTextConnectionId.startsWith("text_")

        assertTrue("以text_开头的连接ID应该被识别为文字窗口", isTextWindow1)
        assertFalse("不以text_开头的连接ID不应该被识别为文字窗口", isTextWindow2)

        println("✅ 文字窗口识别逻辑测试通过")
        println("   文字窗口ID: $textConnectionId -> $isTextWindow1")
        println("   非文字窗口ID: $nonTextConnectionId -> $isTextWindow2")
    }

    @Test
    fun testPositionCalculationLogic() {
        // 🎯 测试位置计算逻辑（模拟我们修复后的逻辑）
        val originalX = 100f
        val originalY = 150f
        val rotationAngle = 45f
        val remoteControlScale = 0.8

        // 模拟文字窗口的位置计算（修复后）
        val textWindowX = originalX  // 直接使用接收端位置，不进行旋转补偿
        val textWindowY = originalY  // 直接使用接收端位置，不进行旋转补偿

        val textVisualizedX = textWindowX * remoteControlScale
        val textVisualizedY = textWindowY * remoteControlScale

        // 验证计算结果
        val expectedX = originalX * remoteControlScale
        val expectedY = originalY * remoteControlScale

        assertEquals("文字窗口X位置计算应该正确", expectedX.toDouble(), textVisualizedX.toDouble(), 0.01)
        assertEquals("文字窗口Y位置计算应该正确", expectedY.toDouble(), textVisualizedY.toDouble(), 0.01)

        println("✅ 位置计算逻辑测试通过")
        println("   原始位置: ($originalX, $originalY)")
        println("   旋转角度: ${rotationAngle}°")
        println("   缩放比例: $remoteControlScale")
        println("   可视化位置: ($textVisualizedX, $textVisualizedY)")
        println("   预期位置: ($expectedX, $expectedY)")
    }

    @Test
    fun testMultipleScaleFactors() {
        // 🎯 测试不同缩放比例下的位置计算
        val scaleFactors = listOf(0.5, 0.8, 1.0, 1.2)
        val baseX = 120f
        val baseY = 180f

        scaleFactors.forEach { scale ->
            // 模拟文字窗口的位置计算
            val textWindowX = baseX  // 直接使用接收端位置
            val textWindowY = baseY  // 直接使用接收端位置

            val visualizedX = textWindowX * scale
            val visualizedY = textWindowY * scale

            val expectedX = baseX * scale
            val expectedY = baseY * scale

            assertEquals("缩放${scale}时X位置应该正确", expectedX.toDouble(), visualizedX.toDouble(), 0.01)
            assertEquals("缩放${scale}时Y位置应该正确", expectedY.toDouble(), visualizedY.toDouble(), 0.01)

            println("✅ 缩放${scale}测试通过 - 位置: ($visualizedX, $visualizedY)")
        }
    }

    @Test
    fun testDoubleRotationProblem() {
        // 🎯 验证双重旋转问题的修复
        val originalX = 100f
        val originalY = 150f
        val rotationAngle = 45f
        val remoteControlScale = 0.8

        // 🎯 问题分析：
        // 1. 接收端发送的位置已经是旋转后的实际位置
        // 2. 遥控端应该直接使用这个位置进行缩放显示
        // 3. 但不应该再对View应用旋转变换，否则会造成双重旋转

        val isTextWindow = true

        // 模拟修复后的逻辑
        val actualDisplayX = originalX  // 直接使用接收端位置
        val actualDisplayY = originalY  // 直接使用接收端位置

        val visualizedX = actualDisplayX * remoteControlScale
        val visualizedY = actualDisplayY * remoteControlScale

        // View层的旋转设置
        val viewRotation = if (isTextWindow) {
            // 📝 文字窗口：不应用View旋转（修复后）
            0f
        } else {
            // 🖼️ 其他窗口：应用View旋转
            rotationAngle
        }

        val expectedX = originalX * remoteControlScale
        val expectedY = originalY * remoteControlScale

        assertEquals("修复后X位置应该正确", expectedX.toDouble(), visualizedX.toDouble(), 0.01)
        assertEquals("修复后Y位置应该正确", expectedY.toDouble(), visualizedY.toDouble(), 0.01)
        assertEquals("文字窗口View旋转应该为0", 0f, viewRotation, 0.01f)

        println("✅ 双重旋转问题修复验证通过")
        println("   接收端位置（已旋转）: ($originalX, $originalY)")
        println("   旋转角度: ${rotationAngle}°")
        println("   遥控端可视化位置: ($visualizedX, $visualizedY)")
        println("   View层旋转: ${viewRotation}°")
        println("   修复要点: 文字窗口不应用View旋转，避免双重旋转")
    }

    @Test
    fun testTextWindowRotationWithPivotCompensation() {
        // 🎯 最终修复验证：文字窗口的旋转和位置补偿
        val originalX = 120f
        val originalY = 180f
        val rotationAngle = 30f
        val scaleFactor = 1.2f
        val windowWidth = 300f
        val windowHeight = 200f

        // 模拟最终修复后的逻辑
        val isTextWindow = true

        // 1. 位置计算：直接使用接收端位置
        val baseX = originalX
        val baseY = originalY

        // 2. 旋转显示：应用旋转角度
        val viewRotation = rotationAngle

        // 3. pivot点处理：文字窗口需要特殊处理
        val pivotX: Float
        val pivotY: Float
        val finalX: Float
        val finalY: Float

        // 🎯 修改：所有窗口都使用左上角作为旋转和缩放中心点
        pivotX = 0f
        pivotY = 0f
        finalX = baseX
        finalY = baseY

        // 验证修复逻辑
        assertEquals("旋转角度应该正确应用", rotationAngle, viewRotation, 0.01f)
        assertTrue("有旋转的文字窗口应该使用中心pivot", pivotX > 0f && pivotY > 0f)
        assertNotEquals("有旋转时位置应该有补偿", baseX, finalX, 0.01f)

        println("✅ 文字窗口旋转和位置补偿测试通过")
        println("   原始位置: ($baseX, $baseY)")
        println("   旋转角度: ${viewRotation}°")
        println("   pivot点: ($pivotX, $pivotY)")
        println("   最终位置: ($finalX, $finalY)")
        println("   修复要点: 位置准确 + 旋转显示 + pivot补偿")
    }

    @Test
    fun testBoundaryConstraints() {
        // 🎯 测试边界约束修复：处理超出屏幕边界的位置
        val testCases = listOf(
            // 正常位置
            Pair(100f, 150f) to Pair(100f, 150f),
            // 负数位置（超出上边界和左边界）
            Pair(-50f, -30f) to Pair(0f, 0f),
            // 部分超出边界
            Pair(-10f, 200f) to Pair(0f, 200f),
            Pair(300f, -5f) to Pair(300f, 0f)
        )

        testCases.forEach { (input, expected) ->
            val (inputX, inputY) = input
            val (expectedX, expectedY) = expected

            // 模拟边界约束逻辑
            val minX = 0f
            val minY = 0f
            val maxX = Float.MAX_VALUE
            val maxY = Float.MAX_VALUE

            val actualX = inputX.coerceIn(minX, maxX)
            val actualY = inputY.coerceIn(minY, maxY)

            assertEquals("X坐标边界约束应该正确", expectedX, actualX, 0.01f)
            assertEquals("Y坐标边界约束应该正确", expectedY, actualY, 0.01f)

            println("✅ 边界约束测试: ($inputX, $inputY) -> ($actualX, $actualY)")
        }

        println("✅ 边界约束测试全部通过")
        println("   修复要点: 确保文字窗口显示在可视区域内，避免负坐标")
    }

    @Test
    fun testTextWindowBoundaryFix() {
        // 🎯 测试文字窗口边界修复的完整逻辑
        val originalX = -25f  // 负数X坐标
        val originalY = -10f  // 负数Y坐标
        val remoteControlScale = 0.8

        // 模拟文字窗口的边界处理逻辑
        val isTextWindow = true

        val actualDisplayX: Float
        val actualDisplayY: Float

        if (isTextWindow) {
            // 文字窗口：应用边界约束
            val minX = 0f
            val minY = 0f
            val maxX = Float.MAX_VALUE
            val maxY = Float.MAX_VALUE

            actualDisplayX = originalX.coerceIn(minX, maxX)
            actualDisplayY = originalY.coerceIn(minY, maxY)
        } else {
            // 其他窗口：不应用边界约束
            actualDisplayX = originalX
            actualDisplayY = originalY
        }

        val visualizedX = actualDisplayX * remoteControlScale
        val visualizedY = actualDisplayY * remoteControlScale

        // 验证边界修正
        assertTrue("文字窗口X坐标应该被修正为非负数", actualDisplayX >= 0f)
        assertTrue("文字窗口Y坐标应该被修正为非负数", actualDisplayY >= 0f)
        assertEquals("修正后的X坐标应该为0", 0f, actualDisplayX, 0.01f)
        assertEquals("修正后的Y坐标应该为0", 0f, actualDisplayY, 0.01f)

        println("✅ 文字窗口边界修复测试通过")
        println("   原始位置: ($originalX, $originalY)")
        println("   修正后位置: ($actualDisplayX, $actualDisplayY)")
        println("   可视化位置: ($visualizedX, $visualizedY)")
        println("   修复效果: 负坐标被修正为0，确保窗口显示在可视区域内")
    }
}
