package com.example.castapp.ui.view;

/**
 * 自定义TypefaceSpan，支持自定义Typeface
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0019\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010\u0006J\u0018\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\u0003H\u0002J\b\u0010\f\u001a\u0004\u0018\u00010\u0005J\u0006\u0010\r\u001a\u00020\u0003J\u0010\u0010\u000e\u001a\u00020\b2\u0006\u0010\u000f\u001a\u00020\nH\u0016J\u0010\u0010\u0010\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0016R\u0010\u0010\u0004\u001a\u0004\u0018\u00010\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lcom/example/castapp/ui/view/CustomTypefaceSpan;", "Landroid/text/style/MetricAffectingSpan;", "typeface", "Landroid/graphics/Typeface;", "fontItem", "Lcom/example/castapp/utils/FontPresetManager$FontItem;", "(Landroid/graphics/Typeface;Lcom/example/castapp/utils/FontPresetManager$FontItem;)V", "applyCustomTypeface", "", "paint", "Landroid/text/TextPaint;", "tf", "getFontItem", "getTypeface", "updateDrawState", "ds", "updateMeasureState", "app_debug"})
public final class CustomTypefaceSpan extends android.text.style.MetricAffectingSpan {
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Typeface typeface = null;
    @org.jetbrains.annotations.Nullable()
    private final com.example.castapp.utils.FontPresetManager.FontItem fontItem = null;
    
    public CustomTypefaceSpan(@org.jetbrains.annotations.NotNull()
    android.graphics.Typeface typeface, @org.jetbrains.annotations.Nullable()
    com.example.castapp.utils.FontPresetManager.FontItem fontItem) {
        super();
    }
    
    @java.lang.Override()
    public void updateDrawState(@org.jetbrains.annotations.NotNull()
    android.text.TextPaint ds) {
    }
    
    @java.lang.Override()
    public void updateMeasureState(@org.jetbrains.annotations.NotNull()
    android.text.TextPaint paint) {
    }
    
    private final void applyCustomTypeface(android.text.TextPaint paint, android.graphics.Typeface tf) {
    }
    
    /**
     * 获取字体对象
     */
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.Typeface getTypeface() {
        return null;
    }
    
    /**
     * 获取字体项
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.utils.FontPresetManager.FontItem getFontItem() {
        return null;
    }
}