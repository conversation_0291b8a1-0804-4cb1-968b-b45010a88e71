package com.example.castapp.model

import android.graphics.RectF
import androidx.core.graphics.toColorInt

/**
 * 🪟 投屏窗口容器可视化数据类
 * 用于在远程接收端控制窗口中绘制投屏窗口容器的可视化框架
 */
data class WindowVisualizationData(
    val connectionId: String,
    val deviceName: String? = null,
    
    // 原始窗口信息（来自CastWindowInfo）
    val originalX: Float,
    val originalY: Float,
    val originalWidth: Int,
    val originalHeight: Int,
    val rotationAngle: Float,
    val zOrder: Int,
    val scaleFactor: Float,
    val isVisible: Boolean,
    
    // 可视化计算后的数据（二次缩放）
    val visualizedX: Float,
    val visualizedY: Float,
    val visualizedWidth: Float,
    val visualizedHeight: Float,
    
    // 缩放相关参数
    val remoteControlScale: Double, // 远程控制窗口的缩放比例
    val containerScale: Double,     // 投屏窗口容器的二次缩放比例
    
    // 视觉属性
    val alpha: Float = 1.0f,
    val cornerRadius: Float = 16f,
    val isMirrored: Boolean = false,
    val isCropping: Boolean = false,
    val cropRectRatio: RectF? = null,

    // 边框属性
    val isBorderEnabled: Boolean = false,
    val borderColor: Int = "#6B6B6B".toColorInt(),
    val borderWidth: Float = 2f,

    // 🎯 截图数据（非文字窗口使用）
    val screenshotBitmap: android.graphics.Bitmap? = null,

    // 📝 文字内容数据（文字窗口使用）
    val textContent: String = "",

    // 🎯 新增：富文本格式数据（文字窗口使用）
    val richTextData: String? = null,
    val isBold: Boolean = false,
    val isItalic: Boolean = false,
    val fontSize: Int = 13,
    val fontName: String? = null,
    val fontFamily: String? = null,
    val lineSpacing: Float = 0.0f,
    val textAlignment: Int = android.view.Gravity.CENTER,

    // 🎯 新增：窗口背景颜色数据（文字窗口使用）
    val isWindowColorEnabled: Boolean = false,
    val windowBackgroundColor: Int = 0xFFFFFFFF.toInt()
) {
    
    /**
     * 获取简化的连接ID（显示规则）
     * - 摄像头窗口：显示完整ID
     * - 媒体窗口：显示文件名前8位
     * - 文本窗口：显示UUID后8位
     * - 投屏窗口：显示后8位
     */
    fun getShortConnectionId(): String {
        return when {
            connectionId == "front_camera" -> "front_camera"
            connectionId == "rear_camera" -> "rear_camera"
            connectionId.startsWith("video_") || connectionId.startsWith("image_") -> {
                // 媒体窗口：提取文件名部分并显示前8位
                val parts = connectionId.split("_", limit = 2)
                if (parts.size >= 2) {
                    val fileName = parts[1]
                    if (fileName.length > 8) fileName.take(8) else fileName
                } else {
                    connectionId.takeLast(8)
                }
            }
            connectionId.startsWith("text_") -> {
                // 文本窗口：显示UUID后8位
                connectionId.takeLast(8)
            }
            else -> connectionId.takeLast(8)
        }
    }
    
    /**
     * 获取设备显示信息
     */
    fun getDeviceDisplayInfo(): String {
        val displayDeviceName = if (!deviceName.isNullOrBlank()) deviceName else "未知设备"
        return "$displayDeviceName(${getShortConnectionId()})"
    }
    
    /**
     * 获取可视化窗口的边界矩形
     */
    fun getVisualizationBounds(): RectF {
        return RectF(
            visualizedX,
            visualizedY,
            visualizedX + visualizedWidth,
            visualizedY + visualizedHeight
        )
    }
    
    /**
     * 检查是否需要显示（可见且有有效尺寸）
     */
    fun shouldDisplay(): Boolean {
        return isVisible && visualizedWidth > 0 && visualizedHeight > 0
    }
    
    /**
     * 获取尺寸显示文本
     */
    fun getSizeText(): String = "${visualizedWidth.toInt()}×${visualizedHeight.toInt()}"
    

    
    companion object {
        
        /**
         * 从CastWindowInfo创建WindowVisualizationData
         * @param windowInfo 投屏窗口信息
         * @param remoteControlScale 远程控制窗口的缩放比例
         * @return 可视化数据对象
         */
        fun fromCastWindowInfo(
            windowInfo: CastWindowInfo,
            remoteControlScale: Double,
            receiverId: String? = null
        ): WindowVisualizationData {
            
            // 🎯 修复：可视化尺寸计算应该基于原始窗口尺寸，不应用裁剪
            // 裁剪效果应该完全由Canvas裁剪来实现，避免双重应用裁剪
            val currentWidth = windowInfo.baseWindowWidth.toFloat()
            val currentHeight = windowInfo.baseWindowHeight.toFloat()

            // 🎯 关键修复：根据窗口类型采用不同的缩放策略
            val isTextWindow = windowInfo.connectionId.startsWith("text_")

            val actualWidth: Int
            val actualHeight: Int

            // 🎯 统一缩放模式：所有窗口都在View层应用缩放，数据层保持原始尺寸
            // 这样与文字窗口的处理方式保持一致，提供统一的缩放体验
            actualWidth = currentWidth.toInt()  // 保持原始尺寸
            actualHeight = currentHeight.toInt() // 保持原始尺寸
            com.example.castapp.utils.AppLog.d("  🎯 统一缩放：所有窗口数据层保持原始尺寸，View层应用缩放")

            // 用于可视化计算的尺寸
            val scaledWidth = actualWidth.toFloat()
            val scaledHeight = actualHeight.toFloat()
            
            // 计算投屏窗口容器的二次缩放比例
            val containerScale = remoteControlScale
            
            // 计算可视化后的尺寸和位置
            val visualizedWidth = (scaledWidth * containerScale).toFloat()
            val visualizedHeight = (scaledHeight * containerScale).toFloat()

            // 🎯 关键分析：接收端发送的是容器位置，需要确定可视化窗口应该显示在哪里
            val containerX = windowInfo.positionX
            val containerY = windowInfo.positionY

            // 🎯 调试：分析位置计算策略
            com.example.castapp.utils.AppLog.d("【可视化位置计算调试】窗口: ${windowInfo.connectionId}")
            com.example.castapp.utils.AppLog.d("  接收端发送的容器位置: ($containerX, $containerY)")
            com.example.castapp.utils.AppLog.d("  是否裁剪: ${windowInfo.isCropping}")
            com.example.castapp.utils.AppLog.d("  是否文字窗口: $isTextWindow")
            com.example.castapp.utils.AppLog.d("  旋转角度: ${windowInfo.rotationAngle}°")

            val actualDisplayX: Float
            val actualDisplayY: Float

            // 🎯 关键修复：文字窗口的位置处理
            if (isTextWindow) {
                // 📝 文字窗口特殊处理：接收端发送的位置已经包含所有变换（包括旋转）
                // 但可能超出屏幕边界，需要进行边界约束

                // 🎯 边界处理：确保文字窗口显示在可视区域内
                val minX = 0f
                val minY = 0f
                // 预留一些边距，确保窗口不会完全超出边界
                val maxX = Float.MAX_VALUE // 暂时不限制右边界
                val maxY = Float.MAX_VALUE // 暂时不限制下边界

                // 应用边界约束
                actualDisplayX = containerX.coerceIn(minX, maxX)
                actualDisplayY = containerY.coerceIn(minY, maxY)

                if (actualDisplayX != containerX || actualDisplayY != containerY) {
                    com.example.castapp.utils.AppLog.d("  📝 文字窗口位置边界修正:")
                    com.example.castapp.utils.AppLog.d("    原始位置: ($containerX, $containerY)")
                    com.example.castapp.utils.AppLog.d("    修正后位置: ($actualDisplayX, $actualDisplayY)")
                } else {
                    com.example.castapp.utils.AppLog.d("  📝 文字窗口：直接使用接收端位置（已包含旋转变换）: ($actualDisplayX, $actualDisplayY)")
                }
            } else if (windowInfo.isCropping && windowInfo.cropRectRatio != null) {
                // 🖼️ 非文字窗口的裁剪处理
                val cropOffsetX = windowInfo.cropRectRatio.left * currentWidth
                val cropOffsetY = windowInfo.cropRectRatio.top * currentHeight

                // 🎯 当前策略：显示在容器位置（与接收端发送的位置一致）
                actualDisplayX = containerX
                actualDisplayY = containerY

                com.example.castapp.utils.AppLog.d("  裁剪偏移: ($cropOffsetX, $cropOffsetY)")
                com.example.castapp.utils.AppLog.d("  可见区域位置: (${containerX + cropOffsetX}, ${containerY + cropOffsetY})")
                com.example.castapp.utils.AppLog.d("  🎯 当前策略：可视化窗口显示在容器位置: ($actualDisplayX, $actualDisplayY)")
                com.example.castapp.utils.AppLog.d("  🎯 这意味着可视化窗口位置与用户看到的窗口位置不一致")
            } else {
                // 🖼️ 普通非文字窗口：容器位置就是可见位置
                actualDisplayX = containerX
                actualDisplayY = containerY
                com.example.castapp.utils.AppLog.d("  🖼️ 普通非文字窗口：直接使用容器位置: ($actualDisplayX, $actualDisplayY)")
            }

            // 按远程控制窗口的缩放比例缩放位置
            val visualizedX = (actualDisplayX * remoteControlScale).toFloat()
            val visualizedY = (actualDisplayY * remoteControlScale).toFloat()

            com.example.castapp.utils.AppLog.d("  远程控制缩放: $remoteControlScale")
            com.example.castapp.utils.AppLog.d("  最终可视化位置: ($visualizedX, $visualizedY)")
            
            // 🔍 添加详细的尺寸计算日志
            com.example.castapp.utils.AppLog.d("【尺寸计算】窗口: ${windowInfo.getDisplayTextWithDevice()}")
            com.example.castapp.utils.AppLog.d("  基础尺寸: ${windowInfo.baseWindowWidth}×${windowInfo.baseWindowHeight}")
            com.example.castapp.utils.AppLog.d("  是否裁剪: ${windowInfo.isCropping}")
            windowInfo.cropRectRatio?.let { cropRatio ->
                val cropWidth = cropRatio.right - cropRatio.left
                val cropHeight = cropRatio.bottom - cropRatio.top
                com.example.castapp.utils.AppLog.d("  裁剪比例: $cropWidth × $cropHeight")
                com.example.castapp.utils.AppLog.d("  🎯 注意: 裁剪效果将由Canvas裁剪实现，不影响容器尺寸")
            }
            com.example.castapp.utils.AppLog.d("  窗口缩放: ${windowInfo.scaleFactor}")
            com.example.castapp.utils.AppLog.d("  🎯 容器尺寸（基于原始窗口）: ${actualWidth}×${actualHeight}")
            com.example.castapp.utils.AppLog.d("  远程控制缩放: ${"%.6f".format(remoteControlScale)}")
            com.example.castapp.utils.AppLog.d("  可视化尺寸: ${visualizedWidth.toInt()}×${visualizedHeight.toInt()}")
            com.example.castapp.utils.AppLog.d("  缩放验证: $actualWidth × ${"%.6f".format(remoteControlScale)} = ${(actualWidth * remoteControlScale).toInt()}")
            com.example.castapp.utils.AppLog.d("  接收端容器位置: (${windowInfo.positionX}, ${windowInfo.positionY})")
            com.example.castapp.utils.AppLog.d("  🎯 直接使用容器位置: ($actualDisplayX, $actualDisplayY)")
            com.example.castapp.utils.AppLog.d("  可视化位置: (${visualizedX.toInt()}, ${visualizedY.toInt()})")
            com.example.castapp.utils.AppLog.d("  位置验证: $actualDisplayX × ${"%.6f".format(remoteControlScale)} = ${(actualDisplayX * remoteControlScale).toInt()}")

            // 🎯 添加边框参数调试日志
            com.example.castapp.utils.AppLog.d("【可视化数据转换】边框参数传递:")
            com.example.castapp.utils.AppLog.d("  源数据边框启用: ${windowInfo.isBorderEnabled}")
            com.example.castapp.utils.AppLog.d("  源数据边框颜色: ${String.format("#%08X", windowInfo.borderColor)}")
            com.example.castapp.utils.AppLog.d("  源数据边框宽度: ${windowInfo.borderWidth}dp")

            // 🎯 关键修复：对于文字窗口，从统一配置管理器中获取最新的文字内容
            var textContent = ""
            var richTextData: String? = null
            var isBold = false
            var isItalic = false
            var fontSize = 13
            var fontName: String? = null
            var fontFamily: String? = null
            var lineSpacing = 0.0f
            var textAlignment = android.view.Gravity.CENTER
            var isWindowColorEnabled = false
            var windowBackgroundColor = 0xFFFFFFFF.toInt()

            if (isTextWindow && receiverId != null) {
                try {
                    val configManager = com.example.castapp.model.RemoteWindowConfigManager.getInstance()
                    val textConfig = configManager.getTextWindowConfig(receiverId, windowInfo.connectionId)

                    if (textConfig != null) {
                        textContent = textConfig.textContent
                        richTextData = textConfig.richTextData
                        isBold = textConfig.isBold
                        isItalic = textConfig.isItalic
                        fontSize = textConfig.fontSize
                        fontName = textConfig.fontName
                        fontFamily = textConfig.fontFamily
                        lineSpacing = textConfig.lineSpacing
                        textAlignment = textConfig.textAlignment
                        isWindowColorEnabled = textConfig.isWindowTextColorEnabled
                        windowBackgroundColor = textConfig.windowTextBackgroundColor

                        com.example.castapp.utils.AppLog.d("【可视化数据转换】📝 已从统一配置管理器获取文字内容: ${windowInfo.connectionId}, 内容='$textContent'")
                    } else {
                        com.example.castapp.utils.AppLog.d("【可视化数据转换】📝 统一配置管理器中未找到文字窗口配置: ${windowInfo.connectionId}")
                    }
                } catch (e: Exception) {
                    com.example.castapp.utils.AppLog.e("【可视化数据转换】📝 从统一配置管理器获取文字内容失败: ${windowInfo.connectionId}", e)
                }
            }

            return WindowVisualizationData(
                connectionId = windowInfo.connectionId,
                deviceName = windowInfo.deviceName,
                originalX = actualDisplayX,  // 🎯 修复：接收端发送的容器位置，裁剪由Canvas处理
                originalY = actualDisplayY,  // 🎯 修复：接收端发送的容器位置，裁剪由Canvas处理
                originalWidth = actualWidth,
                originalHeight = actualHeight,
                rotationAngle = windowInfo.rotationAngle,
                zOrder = windowInfo.zOrder,
                scaleFactor = windowInfo.scaleFactor,
                isVisible = windowInfo.isVisible,
                visualizedX = visualizedX,
                visualizedY = visualizedY,
                visualizedWidth = visualizedWidth,
                visualizedHeight = visualizedHeight,
                remoteControlScale = remoteControlScale,
                containerScale = containerScale,
                alpha = windowInfo.alpha,
                cornerRadius = windowInfo.cornerRadius,
                isMirrored = windowInfo.isMirrored,
                isCropping = windowInfo.isCropping,
                cropRectRatio = windowInfo.cropRectRatio,
                // 🎯 添加边框参数传递
                isBorderEnabled = windowInfo.isBorderEnabled,
                borderColor = windowInfo.borderColor,
                borderWidth = windowInfo.borderWidth,
                // 🎯 关键修复：添加文字内容和格式信息
                textContent = textContent,
                richTextData = richTextData,
                isBold = isBold,
                isItalic = isItalic,
                fontSize = fontSize,
                fontName = fontName,
                fontFamily = fontFamily,
                lineSpacing = lineSpacing,
                textAlignment = textAlignment,
                isWindowColorEnabled = isWindowColorEnabled,
                windowBackgroundColor = windowBackgroundColor
            )
        }
    }
}
