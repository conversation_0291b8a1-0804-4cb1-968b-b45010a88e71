package com.example.castapp

import com.example.castapp.websocket.ControlMessage
import org.junit.Test
import org.junit.Assert.*

/**
 * 批量窗口同步功能测试
 */
class BatchWindowSyncTest {

    @Test
    fun testBatchSyncMessageCreation() {
        println("🔄 测试批量同步消息创建")
        
        try {
            // 模拟窗口数据
            val windowsData = listOf(
                mapOf(
                    "connectionId" to "test_window_1",
                    "positionX" to 100f,
                    "positionY" to 200f,
                    "scaleFactor" to 1.5f,
                    "rotationAngle" to 45f,
                    "isVisible" to true,
                    "alpha" to 0.8f
                ),
                mapOf(
                    "connectionId" to "test_window_2",
                    "positionX" to 300f,
                    "positionY" to 400f,
                    "scaleFactor" to 2.0f,
                    "rotationAngle" to 90f,
                    "isVisible" to false,
                    "alpha" to 1.0f
                )
            )
            
            // 创建批量同步消息
            val batchSyncMessage = ControlMessage.createBatchWindowSyncControl(
                connectionId = "test_connection",
                allWindowsData = windowsData
            )
            
            // 验证消息类型
            assertEquals(ControlMessage.TYPE_REMOTE_WINDOW_TRANSFORM_CONTROL, batchSyncMessage.type)
            assertEquals("test_connection", batchSyncMessage.connectionId)
            
            // 验证变换类型
            val transformType = batchSyncMessage.data["transform_type"]
            assertEquals("batch_sync", transformType)
            
            // 验证变换数据
            @Suppress("UNCHECKED_CAST")
            val transformData = batchSyncMessage.data["transform_data"] as Map<String, Any>
            
            @Suppress("UNCHECKED_CAST")
            val receivedWindowsData = transformData["windows_data"] as List<Map<String, Any>>
            assertEquals(2, receivedWindowsData.size)
            
            val syncCount = transformData["sync_count"]
            assertEquals(2, syncCount)
            
            // 验证第一个窗口数据
            val firstWindow = receivedWindowsData[0]
            assertEquals("test_window_1", firstWindow["connectionId"])
            assertEquals(100f, firstWindow["positionX"])
            assertEquals(200f, firstWindow["positionY"])
            assertEquals(1.5f, firstWindow["scaleFactor"])
            assertEquals(45f, firstWindow["rotationAngle"])
            assertEquals(true, firstWindow["isVisible"])
            assertEquals(0.8f, firstWindow["alpha"])
            
            // 验证第二个窗口数据
            val secondWindow = receivedWindowsData[1]
            assertEquals("test_window_2", secondWindow["connectionId"])
            assertEquals(300f, secondWindow["positionX"])
            assertEquals(400f, secondWindow["positionY"])
            assertEquals(2.0f, secondWindow["scaleFactor"])
            assertEquals(90f, secondWindow["rotationAngle"])
            assertEquals(false, secondWindow["isVisible"])
            assertEquals(1.0f, secondWindow["alpha"])
            
            // 验证时间戳存在
            assertTrue(batchSyncMessage.data.containsKey("timestamp"))
            
            println("✅ 批量同步消息创建测试通过")
            
        } catch (e: Exception) {
            println("❌ 批量同步消息创建测试失败: ${e.message}")
            throw e
        }
    }

    @Test
    fun testBatchSyncMessageWithCropData() {
        println("🔄 测试包含裁剪数据的批量同步消息")
        
        try {
            // 模拟包含裁剪数据的窗口
            val windowsData = listOf(
                mapOf(
                    "connectionId" to "cropped_window",
                    "positionX" to 50f,
                    "positionY" to 100f,
                    "scaleFactor" to 1.0f,
                    "rotationAngle" to 0f,
                    "isCropping" to true,
                    "cropRectRatio" to mapOf(
                        "left" to 0.1f,
                        "top" to 0.2f,
                        "right" to 0.9f,
                        "bottom" to 0.8f
                    )
                )
            )
            
            val batchSyncMessage = ControlMessage.createBatchWindowSyncControl(
                connectionId = "test_connection",
                allWindowsData = windowsData
            )
            
            // 验证裁剪数据
            @Suppress("UNCHECKED_CAST")
            val transformData = batchSyncMessage.data["transform_data"] as Map<String, Any>
            @Suppress("UNCHECKED_CAST")
            val receivedWindowsData = transformData["windows_data"] as List<Map<String, Any>>
            
            val windowData = receivedWindowsData[0]
            assertEquals(true, windowData["isCropping"])
            
            @Suppress("UNCHECKED_CAST")
            val cropData = windowData["cropRectRatio"] as Map<String, Any>
            assertEquals(0.1f, cropData["left"])
            assertEquals(0.2f, cropData["top"])
            assertEquals(0.9f, cropData["right"])
            assertEquals(0.8f, cropData["bottom"])
            
            println("✅ 包含裁剪数据的批量同步消息测试通过")
            
        } catch (e: Exception) {
            println("❌ 包含裁剪数据的批量同步消息测试失败: ${e.message}")
            throw e
        }
    }

    @Test
    fun testEmptyWindowsDataHandling() {
        println("🔄 测试空窗口数据处理")
        
        try {
            // 测试空列表
            val emptyWindowsData = emptyList<Map<String, Any>>()
            
            val batchSyncMessage = ControlMessage.createBatchWindowSyncControl(
                connectionId = "test_connection",
                allWindowsData = emptyWindowsData
            )
            
            @Suppress("UNCHECKED_CAST")
            val transformData = batchSyncMessage.data["transform_data"] as Map<String, Any>
            @Suppress("UNCHECKED_CAST")
            val receivedWindowsData = transformData["windows_data"] as List<Map<String, Any>>
            
            assertEquals(0, receivedWindowsData.size)
            assertEquals(0, transformData["sync_count"])
            
            println("✅ 空窗口数据处理测试通过")
            
        } catch (e: Exception) {
            println("❌ 空窗口数据处理测试失败: ${e.message}")
            throw e
        }
    }
}
