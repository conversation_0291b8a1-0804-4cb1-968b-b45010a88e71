package com.example.castapp.utils

import android.text.TextPaint
import android.text.style.MetricAffectingSpan

/**
 * 🎨 自定义字间距样式
 * 用于为文字添加字间距效果，通过调整字符间的间距实现
 * 🎯 修复：改为继承MetricAffectingSpan，避免与其他格式冲突
 */
class LetterSpacingSpan(
    private val letterSpacing: Float // 字间距值，单位为em（相对于字体大小）
) : MetricAffectingSpan() {

    /**
     * 🎯 修复：使用Paint的letterSpacing属性实现字间距
     * 这样可以与其他格式Span协同工作，不会覆盖其他样式
     */
    override fun updateMeasureState(paint: TextPaint) {
        // 应用字间距到Paint对象
        paint.letterSpacing = letterSpacing
        AppLog.v("【字间距Span】应用字间距到Paint: ${letterSpacing}em")
    }

    override fun updateDrawState(paint: TextPaint) {
        // 应用字间距到Paint对象（绘制时）
        paint.letterSpacing = letterSpacing
        AppLog.v("【字间距Span】绘制时应用字间距: ${letterSpacing}em")
    }

    /**
     * 获取字间距值
     */
    fun getLetterSpacing(): Float = letterSpacing

    /**
     * 重写equals方法，用于Span比较
     */
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is LetterSpacingSpan) return false
        return letterSpacing == other.letterSpacing
    }

    /**
     * 重写hashCode方法
     */
    override fun hashCode(): Int {
        return letterSpacing.hashCode()
    }

    /**
     * 重写toString方法，便于调试
     */
    override fun toString(): String {
        return "LetterSpacingSpan(letterSpacing=${letterSpacing}em)"
    }
}
