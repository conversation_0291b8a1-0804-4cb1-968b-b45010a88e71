package com.example.castapp.model

import androidx.core.graphics.toColorInt

/**
 * 投屏窗口信息数据类
 * 用于窗口管理器显示窗口详细信息
 */
data class CastWindowInfo(
    val connectionId: String,
    val ipAddress: String,
    val port: Int,
    val isActive: Boolean = true,
    val deviceName: String? = null,  // 设备名称（品牌 + 型号）
    val note: String? = null,        // 🏷️ 备注信息
    val positionX: Float = 0f,       // 窗口X坐标
    val positionY: Float = 0f,       // 窗口Y坐标
    val scaleFactor: Float = 1.0f,   // 缩放倍数
    val rotationAngle: Float = 0f,   // 旋转角度
    val zOrder: Int = 0,             // 窗口层级（z-order），数值越大越在上层
    val isCropping: Boolean = false, // 是否正在裁剪
    val cropRectRatio: android.graphics.RectF? = null, // 裁剪区域比例
    val isDragEnabled: Boolean = false,     // 拖动功能是否启用
    val isScaleEnabled: Boolean = false,    // 缩放功能是否启用
    val isRotationEnabled: Boolean = false, // 旋转功能是否启用
    val isVisible: Boolean = true,          // 窗口是否可见（隐藏开关）
    val isMirrored: Boolean = false,        // 镜像功能是否启用
    val cornerRadius: Float = 16f,          // 圆角半径（dp），默认16dp
    val alpha: Float = 1.0f,                // 透明度（0.0f-1.0f），默认1.0f（完全不透明）
    val isControlEnabled: Boolean = false,  // 精准调控功能是否启用
    val isEditEnabled: Boolean = false,     // 📝 编辑功能是否启用（仅文字窗口）

    val isBorderEnabled: Boolean = false,   // 边框显示是否启用，默认关闭
    val borderColor: Int = "#6B6B6B".toColorInt(), // 边框颜色，默认灰色
    val borderWidth: Float = 2f,            // 边框宽度（dp），默认2dp
    val baseWindowWidth: Int = 0,           // 基础窗口宽度（未缩放）
    val baseWindowHeight: Int = 0,          // 基础窗口高度（未缩放）
    val windowColorEnabled: Boolean = false, // 🎨 窗口背景颜色是否启用
    val windowBackgroundColor: Int = 0xFFFFFFFF.toInt(), // 🎨 窗口背景颜色
    val isLandscapeModeEnabled: Boolean = false // 🎯 横屏模式检测是否启用，默认关闭
) {
    /**
     * 获取显示用的连接地址
     */
    fun getDisplayAddress(): String = "$ipAddress:$port"

    /**
     * 获取简化的连接ID（显示规则）
     * - 摄像头窗口：显示完整ID
     * - 媒体窗口：显示文件名前8位
     * - 文本窗口：显示UUID后8位
     * - 投屏窗口：显示后8位
     */
    fun getShortConnectionId(): String {
        return when {
            connectionId == "front_camera" -> "front_camera"
            connectionId == "rear_camera" -> "rear_camera"
            connectionId.startsWith("video_") || connectionId.startsWith("image_") -> {
                // 媒体窗口：提取文件名部分并显示前8位
                val parts = connectionId.split("_", limit = 2)
                if (parts.size >= 2) {
                    val fileName = parts[1]
                    if (fileName.length > 8) fileName.take(8) else fileName
                } else {
                    connectionId.takeLast(8)
                }
            }
            connectionId.startsWith("text_") -> {
                // 文本窗口：显示UUID后8位
                connectionId.takeLast(8)
            }
            else -> connectionId.takeLast(8)
        }
    }

    /**
     * 获取完整的显示文本
     */
    fun getDisplayText(): String = "${getDisplayAddress()} (${getShortConnectionId()})"

    /**
     * 获取带设备信息的显示文本
     */
    fun getDisplayTextWithDevice(): String {
        return if (!deviceName.isNullOrBlank()) {
            "${getDisplayAddress()} - $deviceName"
        } else {
            getDisplayText()
        }
    }



    /**
     * 🏷️ 获取备注显示文本
     * @return 备注内容，如"无"或"客厅电视"
     */
    fun getNoteDisplayText(): String {
        return if (!note.isNullOrBlank() && note != "无") {
            note
        } else {
            "无"
        }
    }

    /**
     * 获取格式化的变换信息
     * 格式：位置（X,Y） 缩放：1.5  旋转：75° 层级：1
     */
    fun getTransformInfo(): String {
        // 🐾 现在 zOrder 已经是正确的显示层级（1=最顶层）
        return "位置：（${positionX.toInt()},${positionY.toInt()}） 缩放：${"%.1f".format(scaleFactor)}  旋转：${rotationAngle.toInt()}° 层级：$zOrder"
    }

    /**
     * 获取当前实际窗口尺寸（经过裁剪和缩放后的尺寸）
     * 格式：窗口尺寸:108×240
     */
    fun getWindowSizeInfo(): String {
        if (baseWindowWidth <= 0 || baseWindowHeight <= 0) {
            return "窗口尺寸:未知"
        }

        // 计算基础尺寸
        var currentWidth = baseWindowWidth.toFloat()
        var currentHeight = baseWindowHeight.toFloat()

        // 如果有裁剪，先应用裁剪比例
        cropRectRatio?.let { cropRatio ->
            val cropWidth = cropRatio.right - cropRatio.left
            val cropHeight = cropRatio.bottom - cropRatio.top
            currentWidth *= cropWidth
            currentHeight *= cropHeight
        }

        // 再应用缩放倍数
        val actualWidth = (currentWidth * scaleFactor).toInt()
        val actualHeight = (currentHeight * scaleFactor).toInt()

        return "窗口尺寸:${actualWidth}×${actualHeight}"
    }
}
