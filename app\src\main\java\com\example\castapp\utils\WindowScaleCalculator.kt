package com.example.castapp.utils

import com.example.castapp.model.CastWindowInfo
import com.example.castapp.model.WindowVisualizationData

/**
 * 🪟 投屏窗口容器缩放计算工具类
 * 负责计算远程接收端控制窗口中投屏窗口容器的可视化参数
 */
object WindowScaleCalculator {

    /**
     * 批量计算投屏窗口容器的可视化数据
     * 
     * @param windowInfoList 投屏窗口信息列表
     * @param remoteControlScale 远程控制窗口的缩放比例
     * @return 可视化数据列表
     */
    fun calculateWindowVisualizationData(
        windowInfoList: List<CastWindowInfo>,
        remoteControlScale: Double,
        receiverId: String? = null
    ): List<WindowVisualizationData> {
        AppLog.d("【可视化计算】开始计算 ${windowInfoList.size} 个窗口的可视化数据")
        
        val visualizationDataList = windowInfoList.mapIndexed { index, windowInfo ->
            val visualizationData = WindowVisualizationData.fromCastWindowInfo(
                windowInfo = windowInfo,
                remoteControlScale = remoteControlScale,
                receiverId = receiverId
            )
            
            AppLog.d("【可视化计算】窗口 $index: ${windowInfo.getDisplayTextWithDevice()}")
            AppLog.d("  原始位置: (${windowInfo.positionX}, ${windowInfo.positionY})")
            AppLog.d("  原始尺寸: ${windowInfo.baseWindowWidth}×${windowInfo.baseWindowHeight}")
            AppLog.d("  窗口缩放: ${windowInfo.scaleFactor}")
            AppLog.d("  可视化位置: (${visualizationData.visualizedX}, ${visualizationData.visualizedY})")
            AppLog.d("  可视化尺寸: ${visualizationData.visualizedWidth}×${visualizationData.visualizedHeight}")
            AppLog.d("  层级: ${windowInfo.zOrder}, 旋转: ${windowInfo.rotationAngle}°")
            
            visualizationData
        }
        
        AppLog.d("【可视化计算】完成，生成 ${visualizationDataList.size} 个可视化数据对象")
        return visualizationDataList
    }

    /**
     * 🎯 将遥控端坐标转换为接收端实际屏幕坐标
     * @param remoteX 遥控端X坐标（远程控制窗口坐标系）
     * @param remoteY 遥控端Y坐标（远程控制窗口坐标系）
     * @param remoteControlScale 远程控制窗口的缩放比例
     * @return 接收端实际屏幕坐标 Pair(actualX, actualY)
     */
    fun convertRemoteToActualCoordinates(
        remoteX: Float,
        remoteY: Float,
        remoteControlScale: Double
    ): Pair<Float, Float> {
        // 反向缩放计算：遥控端坐标 / 缩放比例 = 接收端实际坐标
        val actualX = (remoteX / remoteControlScale).toFloat()
        val actualY = (remoteY / remoteControlScale).toFloat()

        AppLog.d("【坐标转换】遥控端坐标: ($remoteX, $remoteY)")
        AppLog.d("  缩放比例: $remoteControlScale")
        AppLog.d("  接收端实际坐标: ($actualX, $actualY)")

        return Pair(actualX, actualY)
    }
}
