<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/surface_view_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#EDEDED"/>

    <!-- 右下角功能按钮组 -->
    <LinearLayout
        android:id="@+id/button_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentEnd="true"
        android:orientation="vertical"
        android:layout_marginBottom="8dp"
        android:layout_marginEnd="4dp"
        android:background="#40000000">

        <Button
            android:id="@+id/btn_clear"
            android:layout_width="35dp"
            android:layout_height="40dp"
            android:background="#00FFFFFF"
            android:drawableTop="@drawable/ic_clear"
            android:text="清屏"
            android:drawablePadding="-4dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:textSize="10dp"
            android:textColor="#FFFFFF" />

        <Button
            android:id="@+id/btn_save"
            android:layout_width="35dp"
            android:layout_height="40dp"
            android:background="#00FFFFFF"
            android:drawableTop="@drawable/ic_save"
            android:text="保存"
            android:drawablePadding="-4dp"
            android:layout_marginBottom="8dp"
            android:textSize="10dp"
            android:textColor="#FFFFFF" />

        <Button
            android:id="@+id/btn_director"
            android:layout_width="35dp"
            android:layout_height="40dp"
            android:background="#00FFFFFF"
            android:drawableTop="@drawable/ic_director"
            android:text="导播"
            android:drawablePadding="-4dp"
            android:layout_marginBottom="8dp"
            android:textSize="10dp"
            android:textColor="#FFFFFF" />

        <Button
            android:id="@+id/btn_layer_manager"
            android:layout_width="35dp"
            android:layout_height="40dp"
            android:background="#00FFFFFF"
            android:drawableTop="@drawable/ic_layer"
            android:text="层级"
            android:drawablePadding="-4dp"
            android:layout_marginBottom="8dp"
            android:textSize="10dp"
            android:textColor="#FFFFFF" />

        <Button
            android:id="@+id/btn_window_manager"
            android:layout_width="35dp"
            android:layout_height="40dp"
            android:background="#00FFFFFF"
            android:drawableTop="@drawable/ic_window_settings"
            android:text="窗口"
            android:drawablePadding="-4dp"
            android:layout_marginBottom="8dp"
            android:textSize="10dp"
            android:textColor="#FFFFFF" />

        <Button
            android:id="@+id/btn_send"
            android:layout_width="35dp"
            android:layout_height="40dp"
            android:background="#00FFFFFF"
            android:drawableTop="@drawable/ic_send"
            android:text="发送"
            android:drawablePadding="-4dp"
            android:layout_marginBottom="8dp"
            android:textSize="10dp"
            android:textColor="#FFFFFF" />

        <Button
            android:id="@+id/btn_receive"
            android:layout_width="35dp"
            android:layout_height="40dp"
            android:background="#00FFFFFF"
            android:drawableTop="@drawable/ic_cast"
            android:text="接收"
            android:drawablePadding="-4dp"
            android:layout_marginBottom="8dp"
            android:textSize="10dp"
            android:textColor="#FFFFFF" />

        <Button
            android:id="@+id/btn_remote_control"
            android:layout_width="35dp"
            android:layout_height="40dp"
            android:background="#00FFFFFF"
            android:drawableTop="@drawable/ic_remote_control"
            android:text="遥控"
            android:drawablePadding="-4dp"
            android:layout_marginBottom="8dp"
            android:textSize="10dp"
            android:textColor="#FFFFFF" />

        <Button
            android:id="@+id/btn_stopwatch"
            android:layout_width="35dp"
            android:layout_height="40dp"
            android:background="#00FFFFFF"
            android:drawableTop="@drawable/ic_stopwatch"
            android:text="秒表"
            android:drawablePadding="-4dp"
            android:layout_marginBottom="8dp"
            android:textSize="10dp"
            android:textColor="#FFFFFF" />

        <Button
            android:id="@+id/btn_add_media"
            android:layout_width="35dp"
            android:layout_height="40dp"
            android:background="#00FFFFFF"
            android:drawableTop="@drawable/ic_add_media"
            android:text="添加"
            android:drawablePadding="-4dp"
            android:layout_marginBottom="8dp"
            android:textSize="10dp"
            android:textColor="#FFFFFF" />

    </LinearLayout>

    <!-- 动态调控面板容器 - 用于程序化添加多个调控面板 -->

</RelativeLayout>
