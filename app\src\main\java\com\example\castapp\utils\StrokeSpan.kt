package com.example.castapp.utils

import android.graphics.Canvas
import android.graphics.Paint
import android.text.Spannable
import android.text.style.ForegroundColorSpan
import android.text.style.ReplacementSpan

/**
 * 🎨 自定义文字描边样式
 * 用于为文字添加描边效果，实现双重绘制：先描边后填充
 */
class StrokeSpan(
    private val strokeWidth: Float,
    private val strokeColor: Int
) : ReplacementSpan() {

    override fun getSize(paint: Paint, text: CharSequence?, start: Int, end: Int, fm: Paint.FontMetricsInt?): Int {
        if (text == null) {
            AppLog.d("【StrokeSpan】getSize: text为null，返回0")
            return 0
        }

        val textToDraw = text.subSequence(start, end).toString()
        val textWidth = paint.measureText(text, start, end)
        val totalWidth = (textWidth + strokeWidth * 2).toInt()

        // 🎯 关键修复：当Span覆盖整个文本时，必须设置FontMetricsInt，否则draw方法不会被调用
        if (fm != null) {
            // 获取原始字体度量
            val originalFm = paint.fontMetricsInt
            if (originalFm != null) {
                fm.top = originalFm.top
                fm.ascent = originalFm.ascent
                fm.descent = originalFm.descent
                fm.bottom = originalFm.bottom
                fm.leading = originalFm.leading
                AppLog.d("【StrokeSpan】getSize: 已设置FontMetricsInt，确保draw方法被调用")
            }
        }

        AppLog.d("【StrokeSpan】getSize: 文字='$textToDraw', 范围[$start-$end], 文字宽度=${textWidth}px, 描边宽度=${strokeWidth}px, 总宽度=${totalWidth}px")

        return totalWidth
    }

    override fun draw(
        canvas: Canvas, text: CharSequence?, start: Int, end: Int,
        x: Float, top: Int, y: Int, bottom: Int, paint: Paint
    ) {
        if (text == null) return

        val textToDraw = text.subSequence(start, end).toString()

        AppLog.d("【StrokeSpan】开始绘制描边文字: '$textToDraw', 范围[$start-$end]")

        // 保存原始Paint状态
        val originalColor = paint.color
        val originalStyle = paint.style
        val originalStrokeWidth = paint.strokeWidth

        AppLog.d("【StrokeSpan】原始Paint状态: 颜色=${String.format("#%08X", originalColor)}, 样式=$originalStyle")

        // 检查是否有ForegroundColorSpan设置的颜色
        val fillColor = getForegroundColor(text, start, end) ?: originalColor

        AppLog.d("【StrokeSpan】最终使用的填充颜色: ${String.format("#%08X", fillColor)} (${if (getForegroundColor(text, start, end) != null) "来自ForegroundColorSpan" else "使用原始Paint颜色"})")

        // 第一次绘制：描边
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = strokeWidth
        paint.color = strokeColor
        canvas.drawText(textToDraw, x + strokeWidth, y.toFloat(), paint)
        AppLog.d("【StrokeSpan】✅ 描边绘制完成: 颜色=${String.format("#%08X", strokeColor)}, 宽度=${strokeWidth}px")

        // 第二次绘制：填充（使用检测到的文字颜色）
        paint.style = Paint.Style.FILL
        paint.color = fillColor
        canvas.drawText(textToDraw, x + strokeWidth, y.toFloat(), paint)
        AppLog.d("【StrokeSpan】✅ 填充绘制完成: 颜色=${String.format("#%08X", fillColor)}")

        // 恢复原始Paint状态
        paint.color = originalColor
        paint.style = originalStyle
        paint.strokeWidth = originalStrokeWidth

        AppLog.d("【StrokeSpan】🎯 描边文字绘制完成: '$textToDraw', 描边=${String.format("#%08X", strokeColor)}, 填充=${String.format("#%08X", fillColor)}")
    }

    /**
     * 获取文字范围内的ForegroundColorSpan颜色
     */
    private fun getForegroundColor(text: CharSequence, start: Int, end: Int): Int? {
        AppLog.d("【StrokeSpan】开始查找颜色Span: text类型=${text::class.simpleName}, 长度=${text.length}, 目标范围[$start-$end]")

        // 🎯 关键修复：支持多种文本类型
        val spannableText = when (text) {
            is android.text.Spanned -> text  // 包括SpannableString, SpannedString等
            is Spannable -> text
            else -> {
                AppLog.d("【StrokeSpan】❌ 文本类型不支持Span: ${text::class.simpleName}")
                return null
            }
        }

        try {
            // 🎯 修复：使用更宽泛的范围查找，确保能找到所有相关的颜色Span
            val colorSpans = spannableText.getSpans(0, spannableText.length, ForegroundColorSpan::class.java)

            AppLog.d("【StrokeSpan】查找颜色Span: 目标范围[$start-$end], 找到${colorSpans.size}个颜色Span")

            // 查找与当前范围有交集的颜色样式
            for (span in colorSpans) {
                val spanStart = spannableText.getSpanStart(span)
                val spanEnd = spannableText.getSpanEnd(span)

                AppLog.d("【StrokeSpan】检查颜色Span: 范围[$spanStart-$spanEnd], 颜色=${String.format("#%08X", span.foregroundColor)}")

                // 🎯 修复：更宽松的范围匹配条件，只要有交集就使用
                // 原条件：spanStart <= start && spanEnd >= end (完全覆盖)
                // 新条件：有任何交集就使用该颜色
                if (spanStart < end && spanEnd > start) {
                    AppLog.d("【StrokeSpan】✅ 找到匹配的ForegroundColorSpan: 范围[$spanStart-$spanEnd], 颜色=${String.format("#%08X", span.foregroundColor)}")
                    return span.foregroundColor
                }
            }

            AppLog.d("【StrokeSpan】❌ 未找到匹配的ForegroundColorSpan")
        } catch (e: Exception) {
            AppLog.w("【StrokeSpan】获取ForegroundColor失败", e)
        }

        return null
    }

    /**
     * 获取描边宽度
     */
    fun getStrokeWidth(): Float = strokeWidth

    /**
     * 获取描边颜色
     */
    fun getStrokeColor(): Int = strokeColor

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is StrokeSpan) return false
        return strokeWidth == other.strokeWidth && strokeColor == other.strokeColor
    }

    override fun hashCode(): Int {
        var result = strokeWidth.hashCode()
        result = 31 * result + strokeColor
        return result
    }

    override fun toString(): String {
        return "StrokeSpan(strokeWidth=$strokeWidth, strokeColor=${String.format("#%08X", strokeColor)})"
    }
}
