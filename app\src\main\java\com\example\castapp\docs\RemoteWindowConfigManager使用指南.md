# RemoteWindowConfigManager 统一配置管理器使用指南

## 🎯 概述

`RemoteWindowConfigManager` 是 CastAPP 项目中用于统一管理遥控端所有窗口参数的核心组件，解决了之前数据分散存储和不一致的问题。

## 🔧 核心功能

### 1. 统一数据源管理
- **接收端数据**：从接收端获取的权威窗口信息
- **可视化数据**：从遥控端可视化组件同步的实时参数
- **本地缓存**：持久化存储的窗口配置

### 2. 数据一致性保证
- 所有窗口参数更新都通过统一接口
- 自动同步到本地存储
- 提供状态流监听数据变化

### 3. 类型安全的配置管理
- 使用 `RemoteWindowConfig` 数据类
- 支持批量操作和单个更新
- 提供数据来源追踪

## 📝 基本使用方法

### 获取管理器实例
```kotlin
val configManager = RemoteWindowConfigManager.getInstance()
```

### 从接收端更新窗口配置
```kotlin
// 当接收端发送窗口信息时
fun onReceiverWindowInfoReceived(receiverId: String, windowInfoList: List<CastWindowInfo>) {
    configManager.updateFromReceiverData(receiverId, windowInfoList)
}
```

### 同步可视化参数
```kotlin
// 当用户在遥控端操作窗口时
fun onVisualizationChanged(receiverId: String, visualizationDataList: List<WindowVisualizationData>) {
    configManager.syncVisualizationParams(receiverId, visualizationDataList)
}
```

### 更新单个窗口参数
```kotlin
// 更新窗口透明度
configManager.updateWindowConfig(receiverId, connectionId) { currentConfig ->
    currentConfig.copy(alpha = newAlpha, dataSource = "user_adjustment")
}
```

### 获取窗口配置
```kotlin
// 获取单个窗口配置
val windowConfig = configManager.getWindowConfig(receiverId, connectionId)

// 获取所有窗口配置
val allConfigs = configManager.getReceiverConfigs(receiverId)

// 获取窗口信息列表（兼容旧接口）
val windowInfoList = configManager.getWindowInfoList(receiverId)
```

### 批量同步数据
```kotlin
// 获取批量同步数据
val batchData = configManager.getBatchSyncData(receiverId)
val message = ControlMessage.createBatchWindowSyncControl(receiverId, batchData)
```

### 本地存储操作
```kotlin
// 保存到本地存储
configManager.saveToStorage(context, receiverId)

// 从本地存储加载
val loaded = configManager.loadFromStorage(context, receiverId)
```

### 监听配置变化
```kotlin
// 使用 StateFlow 监听配置变化
configManager.configStateFlow.collect { configMap ->
    // configMap: Map<String, Map<String, RemoteWindowConfig>>
    // 外层 key 是 receiverId，内层 key 是 connectionId
    configMap[receiverId]?.let { receiverConfigs ->
        updateUI(receiverConfigs.values.toList())
    }
}
```

## 🔄 迁移指南

### 替换旧的缓存系统
```kotlin
// 旧方式
val cache = RemoteWindowInfoCache.getInstance()
cache.saveWindowInfo(context, receiverId, windowInfoList)
val cachedList = cache.loadWindowInfo(context, receiverId)

// 新方式
configManager.updateFromReceiverData(receiverId, windowInfoList)
configManager.saveToStorage(context, receiverId)
val windowInfoList = configManager.getWindowInfoList(receiverId)
```

### 替换分散的参数管理
```kotlin
// 旧方式：多个地方存储参数
private val remoteWindowConfig = mutableMapOf<String, RemoteWindowConfig>()
private var cachedWindowInfoList: List<CastWindowInfo> = emptyList()

// 新方式：统一管理
private val configManager = RemoteWindowConfigManager.getInstance()
```

## 🎯 最佳实践

### 1. 数据流向
```
接收端数据 → updateFromReceiverData() → 统一配置管理器
用户操作 → updateWindowConfig() → 统一配置管理器
可视化变化 → syncVisualizationParams() → 统一配置管理器
```

### 2. 错误处理
```kotlin
try {
    configManager.updateWindowConfig(receiverId, connectionId) { config ->
        config.copy(alpha = newAlpha)
    }
} catch (e: Exception) {
    AppLog.e("更新窗口配置失败", e)
    // 显示错误提示
}
```

### 3. 性能优化
- 批量更新时使用 `updateFromReceiverData()`
- 避免频繁调用 `saveToStorage()`
- 使用 StateFlow 进行响应式 UI 更新

## 🐛 常见问题

### Q: 如何确保数据一致性？
A: 所有数据更新都通过统一配置管理器，避免直接修改缓存或内存数据。

### Q: 如何处理网络断开？
A: 配置管理器会保持本地数据，重连后可以通过批量同步恢复状态。

### Q: 如何调试数据流？
A: 查看 `dataSource` 字段和时间戳，追踪数据来源和更新时间。

## 📊 配置统计
```kotlin
// 获取配置统计信息
val stats = configManager.getConfigStats()
AppLog.d("配置统计: $stats") // 输出：接收端: 2, 窗口: 5
```
