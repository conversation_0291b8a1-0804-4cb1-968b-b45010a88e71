package com.example.castapp.utils

import android.content.Context
import android.content.SharedPreferences
import android.text.SpannableString
import android.text.style.*
import android.graphics.Typeface
import org.json.JSONArray
import org.json.JSONObject

/**
 * 文本格式管理器
 * 负责文本内容和格式的持久化存储
 */
class TextFormatManager(context: Context) {
    
    companion object {
        private const val PREF_NAME = "text_format_preferences"
        private const val KEY_TEXT_CONTENT = "text_content_"
        private const val KEY_IS_BOLD = "is_bold_"
        private const val KEY_IS_ITALIC = "is_italic_"
        private const val KEY_FONT_SIZE = "font_size_"
        private const val KEY_RICH_TEXT_DATA = "rich_text_data_" // 📝 新增：富文本数据

        // 📝 扩展格式字段常量
        private const val KEY_FONT_NAME = "font_name_"
        private const val KEY_FONT_FAMILY = "font_family_"
        private const val KEY_LINE_SPACING = "line_spacing_"
        private const val KEY_TEXT_ALIGNMENT = "text_alignment_"
    }
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    
    /**
     * 文本格式信息数据类
     */
    data class TextFormatInfo(
        val textContent: String,
        val isBold: Boolean,
        val isItalic: Boolean,
        val fontSize: Int = 13, // 默认字号13sp
        val richTextData: String? = null // 📝 新增：富文本数据（JSON格式）
    )

    /**
     * 📝 扩展文本格式信息数据类
     * 包含完整的字体格式信息
     */
    data class ExtendedTextFormatInfo(
        val textContent: String,
        val isBold: Boolean,
        val isItalic: Boolean,
        val fontSize: Int = 13,
        val fontName: String? = null,
        val fontFamily: String? = null,
        val lineSpacing: Float = 0.0f,
        val textAlignment: Int = android.view.Gravity.CENTER,
        val richTextData: String? = null
    )
    
    /**
     * 保存文本格式
     */
    fun saveTextFormat(textId: String, textContent: String, isBold: Boolean, isItalic: Boolean, fontSize: Int = 13) {
        try {
            sharedPreferences.edit().apply {
                putString(KEY_TEXT_CONTENT + textId, textContent)
                putBoolean(KEY_IS_BOLD + textId, isBold)
                putBoolean(KEY_IS_ITALIC + textId, isItalic)
                putInt(KEY_FONT_SIZE + textId, fontSize)
                apply()
            }

            AppLog.d("【文本格式管理器】格式已保存: ID=$textId, 文本=$textContent, 加粗=$isBold, 倾斜=$isItalic, 字号=${fontSize}sp")

        } catch (e: Exception) {
            AppLog.e("【文本格式管理器】保存格式失败: ID=$textId", e)
        }
    }
    
    /**
     * 获取文本格式
     */
    fun getTextFormat(textId: String): TextFormatInfo? {
        return try {
            val textContent = sharedPreferences.getString(KEY_TEXT_CONTENT + textId, null)

            if (textContent != null) {
                val isBold = sharedPreferences.getBoolean(KEY_IS_BOLD + textId, false)
                val isItalic = sharedPreferences.getBoolean(KEY_IS_ITALIC + textId, false)
                val fontSize = sharedPreferences.getInt(KEY_FONT_SIZE + textId, 13)

                val formatInfo = TextFormatInfo(textContent, isBold, isItalic, fontSize)
                AppLog.d("【文本格式管理器】格式已获取: ID=$textId, 文本=$textContent, 加粗=$isBold, 倾斜=$isItalic, 字号=${fontSize}sp")
                formatInfo
            } else {
                AppLog.d("【文本格式管理器】未找到保存的格式: ID=$textId")
                null
            }

        } catch (e: Exception) {
            AppLog.e("【文本格式管理器】获取格式失败: ID=$textId", e)
            null
        }
    }

    /**
     * 📝 保存富文本格式（包含完整的SpannableString信息）
     */
    fun saveRichTextFormat(textId: String, spannableString: SpannableString) {
        try {
            val textContent = spannableString.toString()
            val richTextData = serializeSpannableString(spannableString)

            sharedPreferences.edit().apply {
                putString(KEY_TEXT_CONTENT + textId, textContent)
                putString(KEY_RICH_TEXT_DATA + textId, richTextData)
                apply()
            }

            AppLog.d("【文本格式管理器】富文本格式已保存: ID=$textId, 文本长度=${textContent.length}, 数据大小=${richTextData.length}")

        } catch (e: Exception) {
            AppLog.e("【文本格式管理器】保存富文本格式失败: ID=$textId", e)
        }
    }

    /**
     * 📝 获取富文本格式（返回完整的SpannableString）
     */
    fun getRichTextFormat(textId: String): SpannableString? {
        return try {
            val textContent = sharedPreferences.getString(KEY_TEXT_CONTENT + textId, null)
            val richTextData = sharedPreferences.getString(KEY_RICH_TEXT_DATA + textId, null)

            if (textContent != null && richTextData != null) {
                val spannableString = deserializeSpannableString(textContent, richTextData)
                AppLog.d("【文本格式管理器】富文本格式已获取: ID=$textId, 文本长度=${textContent.length}")
                spannableString
            } else {
                AppLog.d("【文本格式管理器】未找到富文本格式: ID=$textId")
                null
            }

        } catch (e: Exception) {
            AppLog.e("【文本格式管理器】获取富文本格式失败: ID=$textId", e)
            null
        }
    }

    /**
     * 📝 将SpannableString序列化为JSON字符串
     */
    fun serializeSpannableString(spannableString: SpannableString): String {
        try {
            val jsonObject = JSONObject()
            val spansArray = JSONArray()

            // 获取所有的Span
            val spans = spannableString.getSpans(0, spannableString.length, Any::class.java)

            for (span in spans) {
                val spanStart = spannableString.getSpanStart(span)
                val spanEnd = spannableString.getSpanEnd(span)
                val spanFlags = spannableString.getSpanFlags(span)

                val spanObject = JSONObject()
                spanObject.put("start", spanStart)
                spanObject.put("end", spanEnd)
                spanObject.put("flags", spanFlags)

                when (span) {
                    is StyleSpan -> {
                        spanObject.put("type", "style")
                        spanObject.put("style", span.style)
                    }
                    is AbsoluteSizeSpan -> {
                        spanObject.put("type", "fontSize")
                        // 🎯 关键修复：优先保存原始sp值，避免像素转换问题
                        val originalSpValue = extractOriginalSpValue(span)
                        spanObject.put("size", originalSpValue)
                        spanObject.put("dip", true) // 强制使用sp单位
                    }
                    is ForegroundColorSpan -> {
                        spanObject.put("type", "color")
                        spanObject.put("color", span.foregroundColor)
                    }
                    is StrokeSpan -> {
                        spanObject.put("type", "stroke")
                        spanObject.put("strokeWidth", span.getStrokeWidth())
                        spanObject.put("strokeColor", span.getStrokeColor())
                    }
                    is LetterSpacingSpan -> {
                        spanObject.put("type", "letterSpacing")
                        spanObject.put("letterSpacing", span.getLetterSpacing())
                    }
                    is com.example.castapp.ui.view.CustomTypefaceSpan -> {
                        spanObject.put("type", "customTypeface")
                        val fontItem = span.getFontItem()
                        if (fontItem != null) {
                            spanObject.put("fontName", fontItem.name)
                            spanObject.put("fontFamily", fontItem.fontFamily)
                            spanObject.put("isPreset", fontItem.isPreset)
                            if (!fontItem.isPreset && fontItem.filePath != null) {
                                spanObject.put("filePath", fontItem.filePath)
                            }
                            AppLog.d("【文本格式管理器】序列化CustomTypefaceSpan: ${fontItem.name}")
                        } else {
                            AppLog.w("【文本格式管理器】CustomTypefaceSpan没有FontItem信息，跳过")
                            continue
                        }
                    }
                    else -> {
                        // 📝 对于未知的Span类型，跳过不保存
                        AppLog.w("【文本格式管理器】跳过未知Span类型: ${span::class.simpleName}")
                        continue
                    }
                }

                spansArray.put(spanObject)
            }

            jsonObject.put("spans", spansArray)
            return jsonObject.toString()

        } catch (e: Exception) {
            AppLog.e("【文本格式管理器】序列化SpannableString失败", e)
            return "{\"spans\":[]}"
        }
    }

    /**
     * 🎯 提取AbsoluteSizeSpan的原始sp值
     */
    private fun extractOriginalSpValue(span: AbsoluteSizeSpan): Int {
        // 🎯 修复：对于所有情况都直接返回span.size作为sp值
        // 因为我们在序列化时强制设置dip=true，所以size就是sp值
        return span.size
    }

    /**
     * 📝 从JSON字符串反序列化为SpannableString
     */
    fun deserializeSpannableString(textContent: String, richTextData: String): SpannableString {
        try {
            val spannableString = SpannableString(textContent)
            val jsonObject = JSONObject(richTextData)
            val spansArray = jsonObject.getJSONArray("spans")

            for (i in 0 until spansArray.length()) {
                try {
                    val spanObject = spansArray.getJSONObject(i)
                    val start = spanObject.optInt("start", -1)
                    val end = spanObject.optInt("end", -1)
                    val flags = spanObject.optInt("flags", 0)
                    val type = spanObject.optString("type", "")

                    // 确保范围有效
                    if (start < 0 || end > textContent.length || start >= end || type.isEmpty()) {
                        AppLog.w("【文本格式管理器】跳过无效的Span: start=$start, end=$end, type=$type")
                        continue
                    }

                    val span = when (type) {
                        "style" -> {
                            val style = spanObject.optInt("style", Typeface.NORMAL)
                            StyleSpan(style)
                        }
                        "fontSize" -> {
                            val size = spanObject.optInt("size", 13)
                            // 🎯 关键修复：确保反序列化的字号Span使用sp单位
                            AbsoluteSizeSpan(size, true) // 强制使用sp单位
                        }
                        "color" -> {
                            val color = spanObject.optInt("color", android.graphics.Color.BLACK)
                            ForegroundColorSpan(color)
                        }
                        "stroke" -> {
                            val strokeWidth = spanObject.optDouble("strokeWidth", 1.0).toFloat()
                            val strokeColor = spanObject.optInt("strokeColor", android.graphics.Color.BLACK)
                            StrokeSpan(strokeWidth, strokeColor)
                        }
                        "letterSpacing" -> {
                            val letterSpacing = spanObject.optDouble("letterSpacing", 0.0).toFloat()
                            LetterSpacingSpan(letterSpacing)
                        }
                        "customTypeface" -> {
                            val fontName = spanObject.optString("fontName", "")

                            if (fontName.isNotEmpty()) {
                                try {
                                    // 🎯 关键修复：使用FontPresetManager.getFontByName获取本地字体
                                    // 而不是直接使用接收端的文件路径，确保字体路径的正确性
                                    val fontItem = FontPresetManager.getFontByName(fontName)

                                    if (fontItem != null) {
                                        // 加载字体
                                        val typeface = fontItem.loadTypeface()
                                        if (typeface != null) {
                                            AppLog.d("【文本格式管理器】反序列化CustomTypefaceSpan成功: $fontName")
                                            com.example.castapp.ui.view.CustomTypefaceSpan(typeface, fontItem)
                                        } else {
                                            AppLog.w("【文本格式管理器】CustomTypefaceSpan字体加载失败: $fontName")
                                            null
                                        }
                                    } else {
                                        AppLog.w("【文本格式管理器】未找到字体: $fontName")
                                        null
                                    }
                                } catch (e: Exception) {
                                    AppLog.e("【文本格式管理器】CustomTypefaceSpan反序列化失败: $fontName", e)
                                    null
                                }
                            } else {
                                AppLog.w("【文本格式管理器】CustomTypefaceSpan缺少字体名称")
                                null
                            }
                        }
                        else -> {
                            AppLog.w("【文本格式管理器】未知的Span类型: $type")
                            null
                        }
                    }

                    span?.let {
                        spannableString.setSpan(it, start, end, flags)
                    }

                } catch (e: Exception) {
                    AppLog.w("【文本格式管理器】跳过无效的Span对象: ${e.message}")
                    continue
                }
            }

            return spannableString

        } catch (e: Exception) {
            AppLog.e("【文本格式管理器】反序列化SpannableString失败", e)
            return SpannableString(textContent)
        }
    }

    /**
     * 📝 保存扩展文本格式（包含完整的字体格式信息）
     */
    fun saveExtendedTextFormat(
        textId: String,
        textContent: String,
        isBold: Boolean,
        isItalic: Boolean,
        fontSize: Int = 13,
        fontName: String? = null,
        fontFamily: String? = null,
        lineSpacing: Float = 0.0f,
        textAlignment: Int = android.view.Gravity.CENTER
    ) {
        try {
            sharedPreferences.edit().apply {
                putString(KEY_TEXT_CONTENT + textId, textContent)
                putBoolean(KEY_IS_BOLD + textId, isBold)
                putBoolean(KEY_IS_ITALIC + textId, isItalic)
                putInt(KEY_FONT_SIZE + textId, fontSize)
                putString(KEY_FONT_NAME + textId, fontName)
                putString(KEY_FONT_FAMILY + textId, fontFamily)
                putFloat(KEY_LINE_SPACING + textId, lineSpacing)
                putInt(KEY_TEXT_ALIGNMENT + textId, textAlignment)
                apply()
            }

            AppLog.d("【文本格式管理器】扩展格式已保存: ID=$textId, 字体=$fontName, 行间距=${lineSpacing}dp, 对齐=$textAlignment")

        } catch (e: Exception) {
            AppLog.e("【文本格式管理器】保存扩展格式失败: ID=$textId", e)
        }
    }

    /**
     * 📝 获取扩展文本格式（包含完整的字体格式信息）
     */
    fun getExtendedTextFormat(textId: String): ExtendedTextFormatInfo? {
        return try {
            val textContent = sharedPreferences.getString(KEY_TEXT_CONTENT + textId, null)

            if (textContent != null) {
                val isBold = sharedPreferences.getBoolean(KEY_IS_BOLD + textId, false)
                val isItalic = sharedPreferences.getBoolean(KEY_IS_ITALIC + textId, false)
                val fontSize = sharedPreferences.getInt(KEY_FONT_SIZE + textId, 13)
                val fontName = sharedPreferences.getString(KEY_FONT_NAME + textId, null)
                val fontFamily = sharedPreferences.getString(KEY_FONT_FAMILY + textId, null)
                val lineSpacing = sharedPreferences.getFloat(KEY_LINE_SPACING + textId, 0.0f)
                val textAlignment = sharedPreferences.getInt(KEY_TEXT_ALIGNMENT + textId, android.view.Gravity.CENTER)
                val richTextData = sharedPreferences.getString(KEY_RICH_TEXT_DATA + textId, null)

                val formatInfo = ExtendedTextFormatInfo(
                    textContent, isBold, isItalic, fontSize, fontName, fontFamily, lineSpacing, textAlignment, richTextData
                )
                AppLog.d("【文本格式管理器】扩展格式已获取: ID=$textId, 字体=$fontName, 行间距=${lineSpacing}dp")
                formatInfo
            } else {
                AppLog.d("【文本格式管理器】未找到保存的扩展格式: ID=$textId")
                null
            }

        } catch (e: Exception) {
            AppLog.e("【文本格式管理器】获取扩展格式失败: ID=$textId", e)
            null
        }
    }


}
