package com.example.castapp.integration

import com.example.castapp.model.WindowVisualizationData
import com.example.castapp.ui.windowsettings.TransformManager
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * 🧪 缩放功能集成测试
 * 测试遥控端和接收端缩放功能的协同工作
 */
class ScalingIntegrationTest {

    private lateinit var testTextWindow: WindowVisualizationData
    private lateinit var testNormalWindow: WindowVisualizationData

    @Before
    fun setup() {
        
        // 创建测试窗口数据
        testTextWindow = WindowVisualizationData(
            connectionId = "text_integration_test",
            visualizedX = 50f,
            visualizedY = 50f,
            visualizedWidth = 300f,
            visualizedHeight = 200f,
            scaleFactor = 1.0f,
            rotationAngle = 0f,
            zOrder = 1,
            textContent = "集成测试文字内容"
        )
        
        testNormalWindow = WindowVisualizationData(
            connectionId = "normal_integration_test",
            visualizedX = 400f,
            visualizedY = 300f,
            visualizedWidth = 250f,
            visualizedHeight = 180f,
            scaleFactor = 1.0f,
            rotationAngle = 0f,
            zOrder = 2
        )
    }

    @Test
    fun `测试多窗口缩放数据模型`() {
        // 设置多个窗口
        val windowList = listOf(testTextWindow, testNormalWindow)

        // 验证窗口数量
        assertEquals(2, windowList.size, "应有2个测试窗口")

        // 验证文字窗口和普通窗口的区别处理
        assertTrue(testTextWindow.connectionId.startsWith("text_"), "第一个窗口应为文字窗口")
        assertTrue(!testNormalWindow.connectionId.startsWith("text_"), "第二个窗口应为普通窗口")

        // 验证缩放数据独立性
        val scaledTextWindow = testTextWindow.copy(scaleFactor = 2.0f)
        val scaledNormalWindow = testNormalWindow.copy(scaleFactor = 1.5f)

        assertEquals(2.0f, scaledTextWindow.scaleFactor)
        assertEquals(1.5f, scaledNormalWindow.scaleFactor)
        assertEquals(1.0f, testTextWindow.scaleFactor) // 原始数据不变
        assertEquals(1.0f, testNormalWindow.scaleFactor) // 原始数据不变
    }

    @Test
    fun `测试缩放范围边界情况`() {
        val testScales = listOf(
            0.1f,  // 低于最小值
            TransformManager.MIN_SCALE_FACTOR,  // 最小值
            1.0f,  // 默认值
            TransformManager.MAX_SCALE_FACTOR,  // 最大值
            10.0f  // 高于最大值
        )
        
        testScales.forEach { scale ->
            val clampedScale = scale.coerceIn(
                TransformManager.MIN_SCALE_FACTOR,
                TransformManager.MAX_SCALE_FACTOR
            )
            
            assertTrue(clampedScale >= TransformManager.MIN_SCALE_FACTOR, 
                "缩放因子 $scale 限制后应不小于最小值")
            assertTrue(clampedScale <= TransformManager.MAX_SCALE_FACTOR, 
                "缩放因子 $scale 限制后应不大于最大值")
        }
    }

    @Test
    fun `测试文字窗口特殊缩放行为`() {
        // 文字窗口缩放测试
        val originalX = testTextWindow.visualizedX
        val originalY = testTextWindow.visualizedY
        val scaleFactor = 2.0f
        
        // 模拟文字窗口缩放（左上角锚点）
        val scaledTextWindow = testTextWindow.copy(scaleFactor = scaleFactor)
        
        // 验证文字窗口位置保持不变（左上角锚点特性）
        assertEquals(originalX, scaledTextWindow.visualizedX, 
            "文字窗口缩放后X坐标应保持不变")
        assertEquals(originalY, scaledTextWindow.visualizedY, 
            "文字窗口缩放后Y坐标应保持不变")
        assertEquals(scaleFactor, scaledTextWindow.scaleFactor, 
            "文字窗口缩放因子应正确设置")
    }

    @Test
    fun `测试普通窗口中心点缩放行为`() {
        // 普通窗口缩放测试
        val originalCenterX = testNormalWindow.visualizedX + testNormalWindow.visualizedWidth / 2
        val originalCenterY = testNormalWindow.visualizedY + testNormalWindow.visualizedHeight / 2
        val scaleFactor = 1.5f
        
        // 模拟普通窗口缩放（中心点锚点）
        val scaledNormalWindow = testNormalWindow.copy(scaleFactor = scaleFactor)
        
        // 计算缩放后的中心点（理论上应保持不变）
        val newCenterX = scaledNormalWindow.visualizedX + scaledNormalWindow.visualizedWidth / 2
        val newCenterY = scaledNormalWindow.visualizedY + scaledNormalWindow.visualizedHeight / 2
        
        // 验证中心点计算
        assertEquals(originalCenterX, newCenterX, 0.1f, 
            "普通窗口缩放后中心点X坐标应保持不变")
        assertEquals(originalCenterY, newCenterY, 0.1f, 
            "普通窗口缩放后中心点Y坐标应保持不变")
    }

    @Test
    fun `测试缩放数据创建性能`() {
        val startTime = System.currentTimeMillis()

        // 模拟快速连续的缩放数据创建
        val scaledWindows = mutableListOf<WindowVisualizationData>()
        repeat(100) { iteration ->
            val scaleFactor = 1.0f + (iteration % 10) * 0.1f
            val scaledWindow = testTextWindow.copy(scaleFactor = scaleFactor)
            scaledWindows.add(scaledWindow)
        }

        val endTime = System.currentTimeMillis()
        val totalDuration = endTime - startTime

        // 验证性能：100次数据创建应在合理时间内完成
        assertTrue(totalDuration < 100,
            "100次缩放数据创建应在100ms内完成，实际耗时: ${totalDuration}ms")

        // 验证数据正确性
        assertEquals(100, scaledWindows.size, "应创建100个缩放数据对象")
        assertTrue(scaledWindows.all { it.connectionId == testTextWindow.connectionId },
            "所有缩放数据的连接ID应保持一致")
    }

    @Test
    fun `测试多种变换操作的数据一致性`() {
        // 模拟同时进行的操作
        val operations = listOf(
            "scale" to 1.5f,
            "drag" to 50f,
            "scale" to 2.0f,
            "rotate" to 45f,
            "scale" to 0.8f
        )

        var currentWindow = testTextWindow
        operations.forEach { (operation, value) ->
            when (operation) {
                "scale" -> {
                    currentWindow = currentWindow.copy(scaleFactor = value)
                    assertEquals(value, currentWindow.scaleFactor, "缩放因子应正确更新")
                }
                "drag" -> {
                    currentWindow = currentWindow.copy(
                        visualizedX = currentWindow.visualizedX + value,
                        visualizedY = currentWindow.visualizedY + value
                    )
                    assertTrue(currentWindow.visualizedX > testTextWindow.visualizedX, "X坐标应增加")
                    assertTrue(currentWindow.visualizedY > testTextWindow.visualizedY, "Y坐标应增加")
                }
                "rotate" -> {
                    currentWindow = currentWindow.copy(rotationAngle = value)
                    assertEquals(value, currentWindow.rotationAngle, "旋转角度应正确更新")
                }
            }
        }

        // 验证最终状态一致性
        assertEquals("text_integration_test", currentWindow.connectionId, "连接ID应保持不变")
        assertEquals(0.8f, currentWindow.scaleFactor, "最终缩放因子应为最后设置的值")
        assertEquals(45f, currentWindow.rotationAngle, "最终旋转角度应为最后设置的值")
    }

    @Test
    fun `测试极端缩放值处理`() {
        val extremeScales = listOf(
            Float.MIN_VALUE,
            -1.0f,
            0.0f,
            Float.MAX_VALUE,
            Float.POSITIVE_INFINITY,
            Float.NEGATIVE_INFINITY,
            Float.NaN
        )
        
        extremeScales.forEach { scale ->
            val clampedScale = when {
                scale.isNaN() || scale.isInfinite() || scale <= 0 -> 1.0f
                else -> scale.coerceIn(TransformManager.MIN_SCALE_FACTOR, TransformManager.MAX_SCALE_FACTOR)
            }
            
            assertTrue(clampedScale.isFinite(), "处理后的缩放值应为有限数值")
            assertTrue(clampedScale > 0, "处理后的缩放值应大于0")
            assertTrue(clampedScale >= TransformManager.MIN_SCALE_FACTOR, 
                "处理后的缩放值应不小于最小值")
            assertTrue(clampedScale <= TransformManager.MAX_SCALE_FACTOR, 
                "处理后的缩放值应不大于最大值")
        }
    }
}
