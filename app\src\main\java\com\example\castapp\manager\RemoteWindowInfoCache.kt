package com.example.castapp.manager

import android.content.Context
import com.example.castapp.model.CastWindowInfo
import com.example.castapp.utils.AppLog
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * 遥控端窗口信息缓存管理器
 * 负责在遥控端本地保存和维护窗口设置信息
 */
class RemoteWindowInfoCache private constructor() {

    companion object {
        @Volatile
        private var INSTANCE: RemoteWindowInfoCache? = null

        fun getInstance(): RemoteWindowInfoCache {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: RemoteWindowInfoCache().also { INSTANCE = it }
            }
        }
    }

    private val gson = Gson()

    /**
     * 保存窗口信息到本地缓存
     * @param context 上下文
     * @param receiverId 接收端设备ID
     * @param windowInfoList 窗口信息列表
     */
    fun saveWindowInfo(context: Context, receiverId: String, windowInfoList: List<CastWindowInfo>) {
        try {
            val sharedPrefs = context.getSharedPreferences("remote_window_cache", Context.MODE_PRIVATE)
            val cacheKey = "window_info_$receiverId"
            
            // 序列化窗口信息列表
            val jsonString = gson.toJson(windowInfoList)
            
            // 保存到SharedPreferences
            sharedPrefs.edit()
                .putString(cacheKey, jsonString)
                .putLong("${cacheKey}_timestamp", System.currentTimeMillis())
                .apply()
            
            AppLog.d("【遥控端缓存】已保存窗口信息: $receiverId, ${windowInfoList.size} 个窗口")
            
        } catch (e: Exception) {
            AppLog.e("【遥控端缓存】保存窗口信息失败: $receiverId", e)
        }
    }

    /**
     * 从本地缓存加载窗口信息
     * @param context 上下文
     * @param receiverId 接收端设备ID
     * @return 窗口信息列表，如果没有缓存则返回空列表
     */
    fun loadWindowInfo(context: Context, receiverId: String): List<CastWindowInfo> {
        return try {
            val sharedPrefs = context.getSharedPreferences("remote_window_cache", Context.MODE_PRIVATE)
            val cacheKey = "window_info_$receiverId"
            
            val jsonString = sharedPrefs.getString(cacheKey, null)
            val timestamp = sharedPrefs.getLong("${cacheKey}_timestamp", 0)
            
            if (jsonString != null) {
                // 反序列化窗口信息列表
                val type = object : TypeToken<List<CastWindowInfo>>() {}.type
                val windowInfoList: List<CastWindowInfo> = gson.fromJson(jsonString, type)
                
                AppLog.d("【遥控端缓存】已加载窗口信息: $receiverId, ${windowInfoList.size} 个窗口")
                AppLog.d("【遥控端缓存】缓存时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(timestamp)}")
                
                return windowInfoList
            } else {
                AppLog.d("【遥控端缓存】未找到缓存数据: $receiverId")
                return emptyList()
            }
            
        } catch (e: Exception) {
            AppLog.e("【遥控端缓存】加载窗口信息失败: $receiverId", e)
            emptyList()
        }
    }

    /**
     * 检查缓存是否存在
     * @param context 上下文
     * @param receiverId 接收端设备ID
     * @return 是否存在缓存
     */
    fun hasCachedWindowInfo(context: Context, receiverId: String): Boolean {
        return try {
            val sharedPrefs = context.getSharedPreferences("remote_window_cache", Context.MODE_PRIVATE)
            val cacheKey = "window_info_$receiverId"
            val jsonString = sharedPrefs.getString(cacheKey, null)
            val hasCache = !jsonString.isNullOrEmpty()
            
            AppLog.d("【遥控端缓存】检查缓存存在性: $receiverId -> $hasCache")
            hasCache
            
        } catch (e: Exception) {
            AppLog.e("【遥控端缓存】检查缓存失败: $receiverId", e)
            false
        }
    }

    /**
     * 清除指定设备的缓存
     * @param context 上下文
     * @param receiverId 接收端设备ID
     */
    fun clearWindowInfo(context: Context, receiverId: String) {
        try {
            val sharedPrefs = context.getSharedPreferences("remote_window_cache", Context.MODE_PRIVATE)
            val cacheKey = "window_info_$receiverId"
            
            sharedPrefs.edit()
                .remove(cacheKey)
                .remove("${cacheKey}_timestamp")
                .apply()
            
            AppLog.d("【遥控端缓存】已清除窗口信息缓存: $receiverId")
            
        } catch (e: Exception) {
            AppLog.e("【遥控端缓存】清除缓存失败: $receiverId", e)
        }
    }

    /**
     * 获取缓存时间戳
     * @param context 上下文
     * @param receiverId 接收端设备ID
     * @return 缓存时间戳，如果没有缓存则返回0
     */
    fun getCacheTimestamp(context: Context, receiverId: String): Long {
        return try {
            val sharedPrefs = context.getSharedPreferences("remote_window_cache", Context.MODE_PRIVATE)
            val cacheKey = "window_info_$receiverId"
            sharedPrefs.getLong("${cacheKey}_timestamp", 0)
        } catch (e: Exception) {
            AppLog.e("【遥控端缓存】获取缓存时间戳失败: $receiverId", e)
            0
        }
    }
}
