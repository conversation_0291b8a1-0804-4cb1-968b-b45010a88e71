# 富文本格式同步功能测试方案

## 测试目标
验证接收端到遥控端的富文本格式同步是否正确工作，确保遥控端能够完整显示接收端文字窗口的富文本格式。

## 测试环境准备
1. 准备两台设备：一台作为接收端，一台作为遥控端
2. 确保两台设备在同一网络环境下
3. 在接收端设备上启动CastAPP应用
4. 在遥控端设备上启动CastAPP应用并连接到接收端

## 测试用例

### 测试用例1：基本富文本格式
**测试步骤：**
1. 在接收端添加一个文字窗口A
2. 编辑文字窗口A，输入文本："这是测试文本"
3. 选中部分文字，应用以下格式：
   - 将"测试"两字设置为粗体
   - 将"文本"两字设置为斜体
   - 调整字体大小为18sp
4. 在遥控端连接到接收端
5. 在遥控端的远程接收端控制窗口中点击"获取文字内容"

**预期结果：**
- 遥控端显示的文字窗口A应该包含相同的文本内容
- "测试"两字应该显示为粗体
- "文本"两字应该显示为斜体
- 整体字体大小应该为18sp

### 测试用例2：颜色和字体格式
**测试步骤：**
1. 在接收端的文字窗口A中继续编辑
2. 选中"这是"两字，设置为红色
3. 选中"测试文本"，设置为蓝色
4. 更改整体字体为不同的字体系列（如果支持）
5. 在遥控端刷新文字内容

**预期结果：**
- "这是"两字应该显示为红色
- "测试文本"应该显示为蓝色，同时保持之前的粗体和斜体格式
- 字体系列应该与接收端一致

### 测试用例3：行间距和对齐方式
**测试步骤：**
1. 在接收端的文字窗口A中添加多行文本
2. 调整行间距为2.0倍
3. 设置文本对齐方式为居中
4. 在遥控端刷新文字内容

**预期结果：**
- 遥控端显示的文本应该具有相同的行间距
- 文本对齐方式应该为居中

### 测试用例4：复杂混合格式
**测试步骤：**
1. 在接收端创建一个新的文字窗口B
2. 输入复杂的富文本内容，包含：
   - 不同字体大小的文字
   - 混合的粗体、斜体格式
   - 多种颜色
   - 不同的对齐方式
3. 在遥控端获取文字内容

**预期结果：**
- 所有格式应该完整同步到遥控端
- 文字窗口B应该与接收端显示完全一致

### 测试用例5：格式更新同步
**测试步骤：**
1. 在遥控端已连接的状态下
2. 在接收端修改文字窗口A的格式
3. 在遥控端重新获取文字内容

**预期结果：**
- 遥控端应该显示更新后的格式
- 所有格式变更应该正确同步

## 测试验证点

### 功能验证
- [ ] 纯文本内容正确传输
- [ ] 粗体格式正确显示
- [ ] 斜体格式正确显示
- [ ] 字体大小正确应用
- [ ] 字体颜色正确显示
- [ ] 字体系列正确应用
- [ ] 行间距正确设置
- [ ] 文本对齐正确应用
- [ ] 混合格式正确处理
- [ ] 格式更新正确同步

### 性能验证
- [ ] 富文本数据传输速度合理
- [ ] 格式解析性能良好
- [ ] 内存使用正常
- [ ] 无明显卡顿现象

### 错误处理验证
- [ ] 格式数据损坏时的降级处理
- [ ] 网络传输错误时的处理
- [ ] 不支持的格式的处理
- [ ] 异常情况下的稳定性

## 调试信息检查
在测试过程中，注意检查以下日志信息：
1. `【文本窗口管理器】完整格式信息已获取` - 确认接收端格式信息收集正确
2. `【遥控端格式解析器】富文本格式解析成功` - 确认遥控端解析正确
3. `【窗口容器View】已应用富文本格式` - 确认格式应用成功

## 问题排查
如果测试失败，按以下步骤排查：
1. 检查网络连接是否正常
2. 查看应用日志中的错误信息
3. 验证富文本数据是否正确序列化
4. 检查遥控端解析逻辑是否正确
5. 确认TextView格式应用是否成功
