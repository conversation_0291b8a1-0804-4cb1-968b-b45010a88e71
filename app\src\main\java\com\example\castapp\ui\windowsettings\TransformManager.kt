package com.example.castapp.ui.windowsettings

import android.graphics.RectF
import android.widget.FrameLayout
import com.example.castapp.ui.windowsettings.interfaces.TransformStateListener
import com.example.castapp.utils.AppLog
import kotlin.math.max
import kotlin.math.min

/**
 * 变换状态管理器
 * 负责所有变换状态的管理和计算
 */
class TransformManager(
    private val container: FrameLayout,
    private val windowWidth: Int,
    private val windowHeight: Int
) {
    
    companion object {
        // 缩放手势相关常量
        const val MIN_SCALE_FACTOR = 0.2f  // 最小缩放比例
        const val MAX_SCALE_FACTOR = 8.0f  // 最大缩放比例
    }
    
    // 变换状态参数（用于状态查询和增量计算）
    private var currentTranslationX = 0f
    private var currentTranslationY = 0f
    private var currentScaleFactor = 1.0f
    private var currentRotation = 0f
    private var currentPivotX = 0f
    private var currentPivotY = 0f
    
    // 裁剪模式前的变换状态保存
    private var savedTranslationX = 0f
    private var savedTranslationY = 0f
    private var savedScaleFactor = 1.0f
    private var savedRotation = 0f
    private var savedPivotX = 0f
    private var savedPivotY = 0f
    private var savedIsCroppedWindow = false
    private var savedCropRectRatio: RectF? = null
    
    // 变换变化监听器
    private var transformStateListener: TransformStateListener? = null

    // 🎯 父容器边框法：边框位置更新回调
    private var borderPositionUpdateCallback: (() -> Unit)? = null

    // 🎯 新增：WindowPositionManager引用，用于集中化位置管理
    var positionManager: WindowPositionManager? = null

    // 🎯 新增：裁剪状态回调，用于获取当前裁剪信息
    private var cropStateProvider: (() -> Pair<Boolean, RectF?>)? = null

    // 拖动实时更新控制
    private var lastNotifyTime = 0L
    private val notifyIntervalMs = 100L // 100ms间隔，避免过于频繁
    
    init {
        // 🎯 旋转坐标修复：初始化变换中心点为容器左上角，确保旋转后位置保持不变
        currentPivotX = 0f
        currentPivotY = 0f

        AppLog.d("🎯 TransformManager初始化完成，窗口尺寸: ${windowWidth}x${windowHeight}")
        AppLog.d("🎯 旋转中心点设置为左上角: (0, 0)，确保旋转后拖动坐标一致")
    }
    
    /**
     * 设置变换状态监听器
     */
    fun setTransformStateListener(listener: TransformStateListener?) {
        this.transformStateListener = listener
    }

    /**
     * 🎯 统一边框法：设置边框位置更新回调
     */
    fun setBorderPositionUpdateCallback(callback: (() -> Unit)?) {
        borderPositionUpdateCallback = callback
    }

    /**
     * 🎯 新增：设置裁剪状态提供者
     */
    fun setCropStateProvider(provider: (() -> Pair<Boolean, RectF?>)?) {
        this.cropStateProvider = provider
    }

    /**
     * 🎯 获取裁剪状态提供者（供TransformHandler使用）
     */
    fun getCropStateProvider(): (() -> Pair<Boolean, RectF?>)? {
        return cropStateProvider
    }

    /**
     * 获取当前缩放倍数
     */
    fun getCurrentScaleFactor(): Float = currentScaleFactor
    
    /**
     * 获取当前旋转角度
     */
    fun getCurrentRotation(): Float = currentRotation
    
    /**
     * 获取当前变换中心点X
     */
    fun getCurrentPivotX(): Float = currentPivotX
    
    /**
     * 获取当前变换中心点Y
     */
    fun getCurrentPivotY(): Float = currentPivotY
    
    /**
     * 更新位移（集中化版本：通过WindowPositionManager处理）
     */
    fun updateTranslation(deltaX: Float, deltaY: Float) {
        // 🎯 集中化：通过位置管理器处理位置更新
        positionManager?.updatePosition(deltaX, deltaY)

        // 同步更新内部变量（从容器获取实际位置）
        currentTranslationX = container.translationX
        currentTranslationY = container.translationY

        // 🎯 统一边框法：位置变化时通知边框更新
        borderPositionUpdateCallback?.invoke()

        AppLog.v("🎯 位移更新（集中化版）: 新位置=(${container.translationX}, ${container.translationY})")
    }
    
    /**
     * 更新缩放（带范围限制）
     */
    fun updateScale(scaleFactor: Float): Boolean {
        val newScaleFactor = currentScaleFactor * scaleFactor
        val clampedScaleFactor = max(MIN_SCALE_FACTOR, min(newScaleFactor, MAX_SCALE_FACTOR))
        
        if (clampedScaleFactor != currentScaleFactor) {
            currentScaleFactor = clampedScaleFactor
            return true
        }
        return false
    }
    
    /**
     * 更新旋转角度
     */
    fun updateRotation(rotationDelta: Float) {
        currentRotation += rotationDelta
        
        // 将角度限制在 -180 到 180 度之间
        currentRotation = ((currentRotation + 180) % 360 - 180).let {
            if (it < -180) it + 360 else it
        }
    }
    
    /**
     * 设置变换中心点
     */
    fun setPivot(pivotX: Float, pivotY: Float) {
        currentPivotX = pivotX
        currentPivotY = pivotY
    }
    
    /**
     * 🎯 旋转坐标修复：重置变换中心点为容器左上角
     */
    fun resetPivotToTopLeft() {
        currentPivotX = 0f
        currentPivotY = 0f
    }

    /**
     * 获取实际显示的左上角X坐标（裁剪可见区域适配版）
     */
    fun getActualDisplayX(): Float {
        val translationX = container.translationX
        val scaleX = container.scaleX
        val pivotX = container.pivotX

        // 计算容器的左上角位置
        val containerX = translationX + pivotX * (1 - scaleX)

        // 🎯 关键修复：如果窗口被裁剪，需要计算可见区域的左上角位置
        val (isCroppedWindow, cropRectRatio) = cropStateProvider?.invoke() ?: Pair(false, null)

        val actualX = if (isCroppedWindow && cropRectRatio != null) {
            // 裁剪窗口：容器位置 + 裁剪偏移 = 可见区域左上角
            val cropOffsetX = cropRectRatio.left * windowWidth
            containerX + cropOffsetX
        } else {
            // 非裁剪窗口：直接使用容器位置
            containerX
        }

        AppLog.v("🎯 [可见区域适配] 实际显示X坐标: 容器X=${containerX}, 裁剪=${isCroppedWindow}, 偏移=${if (isCroppedWindow && cropRectRatio != null) cropRectRatio.left * windowWidth else 0f}, 实际X=${actualX}")
        return actualX
    }

    /**
     * 获取实际显示的左上角Y坐标（裁剪可见区域适配版）
     */
    fun getActualDisplayY(): Float {
        val translationY = container.translationY
        val scaleY = container.scaleY
        val pivotY = container.pivotY

        // 计算容器的左上角位置
        val containerY = translationY + pivotY * (1 - scaleY)

        // 🎯 关键修复：如果窗口被裁剪，需要计算可见区域的左上角位置
        val (isCroppedWindow, cropRectRatio) = cropStateProvider?.invoke() ?: Pair(false, null)

        val actualY = if (isCroppedWindow && cropRectRatio != null) {
            // 裁剪窗口：容器位置 + 裁剪偏移 = 可见区域左上角
            val cropOffsetY = cropRectRatio.top * windowHeight
            containerY + cropOffsetY
        } else {
            // 非裁剪窗口：直接使用容器位置
            containerY
        }

        AppLog.v("🎯 [可见区域适配] 实际显示Y坐标: 容器Y=${containerY}, 裁剪=${isCroppedWindow}, 偏移=${if (isCroppedWindow && cropRectRatio != null) cropRectRatio.top * windowHeight else 0f}, 实际Y=${actualY}")
        return actualY
    }

    /**
     * 获取容器的左上角X坐标（用于遥控端可视化）
     * 🎯 新增：专门为遥控端提供容器位置，不考虑裁剪偏移
     */
    fun getContainerDisplayX(): Float {
        val translationX = container.translationX
        val scaleX = container.scaleX
        val pivotX = container.pivotX

        // 计算容器的左上角位置（不考虑裁剪偏移）
        val containerX = translationX + pivotX * (1 - scaleX)

        AppLog.v("🎯 [容器位置] 容器X坐标: ${containerX}")
        return containerX
    }

    /**
     * 获取容器的左上角Y坐标（用于遥控端可视化）
     * 🎯 新增：专门为遥控端提供容器位置，不考虑裁剪偏移
     */
    fun getContainerDisplayY(): Float {
        val translationY = container.translationY
        val scaleY = container.scaleY
        val pivotY = container.pivotY

        // 计算容器的左上角位置（不考虑裁剪偏移）
        val containerY = translationY + pivotY * (1 - scaleY)

        AppLog.v("🎯 [容器位置] 容器Y坐标: ${containerY}")
        return containerY
    }

    /**
     * 设置精准变换（坐标系修复版：正确处理裁剪窗口的坐标转换）
     * @param x 期望的实际显示X坐标（左上角，以屏幕左上角为原点）
     * @param y 期望的实际显示Y坐标（左上角，以屏幕左上角为原点）
     * @param scale 缩放倍数
     * @param rotation 旋转角度
     */
    fun setPrecisionTransform(x: Float, y: Float, scale: Float, rotation: Float) {
        // 设置缩放和旋转
        currentScaleFactor = scale.coerceIn(MIN_SCALE_FACTOR, MAX_SCALE_FACTOR)
        currentRotation = ((rotation % 360f) + 360f) % 360f

        // 🎯 关键修复：直接使用容器的实际pivot点，确保与getActualDisplayX/Y完全一致
        val pivotX = container.pivotX
        val pivotY = container.pivotY

        // 🎯 坐标系修复：获取当前裁剪状态，正确处理坐标转换
        val (isCroppedWindow, cropRectRatio) = cropStateProvider?.invoke() ?: Pair(false, null)

        // 🎯 关键修复：根据窗口类型正确转换坐标系
        val baseX: Float
        val baseY: Float

        // 🎯 修复：遥控端发送的是容器位置，接收端直接应用到容器
        baseX = x
        baseY = y

        if (isCroppedWindow && cropRectRatio != null) {
            AppLog.d("🎯 [裁剪窗口修复] 直接应用容器位置: ($x, $y)")
            AppLog.d("🎯   裁剪比例: left=${cropRectRatio.left}, top=${cropRectRatio.top}")
        } else {
            AppLog.d("🎯 [普通窗口] 直接应用容器位置: ($x, $y)")
        }

        // 计算考虑缩放影响的正确translationX/Y
        // 反向公式：translation = 基准位置 - pivot × (1 - scale)
        val translationX = baseX - pivotX * (1 - currentScaleFactor)
        val translationY = baseY - pivotY * (1 - currentScaleFactor)

        // 🎯 集中化：通过位置管理器设置计算后的translation位置
        container.x = 0f
        container.y = 0f
        positionManager?.setPrecisionPosition(translationX, translationY)

        // 同步更新内部变量
        currentTranslationX = translationX
        currentTranslationY = translationY

        AppLog.d("🎯 精准变换已设置（坐标系修复版）")
        AppLog.d("🎯   传入位置: ($x, $y)")
        AppLog.d("🎯   基准位置: ($baseX, $baseY)")
        AppLog.d("🎯   裁剪状态: $isCroppedWindow, 裁剪比例: $cropRectRatio")
        AppLog.d("🎯   缩放中心点: (${pivotX}, ${pivotY})")
        AppLog.d("🎯   计算后translation: (${translationX}, ${translationY})")
        AppLog.d("🎯   实际translation设置: (${container.translationX}, ${container.translationY})")
        AppLog.d("🎯   缩放: $currentScaleFactor, 旋转: $currentRotation")
    }

    /**
     * 重置变换（集中化版本：通过WindowPositionManager处理位置重置）
     */
    fun resetTransform() {
        // 🎯 集中化：通过位置管理器重置位置
        container.x = 0f
        container.y = 0f
        positionManager?.resetPosition()

        currentTranslationX = 0f
        currentTranslationY = 0f
        currentScaleFactor = 1.0f
        currentRotation = 0f
        currentPivotX = windowWidth / 2f
        currentPivotY = windowHeight / 2f

        AppLog.d("🎯 变换已重置（集中化版）: 位置重置到屏幕左上角(0,0)")
    }

    /**
     * 保存当前变换状态（集中化版本：通过WindowPositionManager处理位置保存）
     */
    fun saveCurrentTransformState(isCroppedWindow: Boolean, cropRectRatio: RectF?) {
        // 🎯 集中化：通过位置管理器保存位置状态
        positionManager?.savePositionState()

        // 🎯 简化：直接保存当前位置，不再进行复杂的偏移计算
        savedTranslationX = container.translationX
        savedTranslationY = container.translationY

        AppLog.d("🎯 [简化状态保存] 直接保存当前位置: (${savedTranslationX}, ${savedTranslationY})")

        savedScaleFactor = currentScaleFactor
        savedRotation = currentRotation
        savedPivotX = currentPivotX
        savedPivotY = currentPivotY
        savedIsCroppedWindow = isCroppedWindow
        savedCropRectRatio = cropRectRatio?.let { RectF(it) } // 深拷贝原始裁剪区域

        // 🎯 同步更新内部变量为原始位置
        currentTranslationX = savedTranslationX
        currentTranslationY = savedTranslationY

        AppLog.d("🎯 已保存变换状态: 原始位移=(${savedTranslationX}, ${savedTranslationY}), 缩放=${savedScaleFactor}, 旋转=${savedRotation}°, 裁剪=${savedCropRectRatio}")
    }

    /**
     * 恢复保存的变换状态
     */
    fun restoreSavedTransformState(): Pair<Boolean, RectF?> {
        currentTranslationX = savedTranslationX
        currentTranslationY = savedTranslationY
        currentScaleFactor = savedScaleFactor
        currentRotation = savedRotation
        currentPivotX = savedPivotX
        currentPivotY = savedPivotY
        val restoredIsCroppedWindow = savedIsCroppedWindow
        val restoredCropRectRatio = savedCropRectRatio?.let { RectF(it) } // 恢复原始裁剪区域
        AppLog.d("🎯 已恢复保存的变换状态: 位移=(${currentTranslationX}, ${currentTranslationY}), 缩放=${currentScaleFactor}, 旋转=${currentRotation}°, 裁剪=${restoredCropRectRatio}")
        return Pair(restoredIsCroppedWindow, restoredCropRectRatio)
    }



    /**
     * 通知变换变化（带频率控制）
     */
    fun notifyTransformChange(connectionId: String, forceNotify: Boolean = false) {
        if (connectionId.isEmpty()) return

        val currentTime = System.currentTimeMillis()
        if (forceNotify || currentTime - lastNotifyTime >= notifyIntervalMs) {
            // 传递实际显示位置，而不是变换偏移量
            transformStateListener?.onTransformChanged(
                connectionId,
                getActualDisplayX(),
                getActualDisplayY(),
                currentScaleFactor,
                currentRotation
            )
            lastNotifyTime = currentTime
        }
    }

    /**
     * 立即通知变换变化（不受频率限制）
     */
    fun notifyTransformChangeImmediate(connectionId: String) {
        notifyTransformChange(connectionId, forceNotify = true)
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        transformStateListener = null
        AppLog.d("TransformManager清理完成")
    }
}
