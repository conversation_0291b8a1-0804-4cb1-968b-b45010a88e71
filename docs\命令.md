# 📌 Android Studio 终端 Gradle 命令速查表

在 Android Studio 底部的 **Terminal** 中运行以下命令。  
（Windows 下去掉 `./`，直接用 `gradlew`）

---

## 🔹 基本构建
```bash
./gradlew clean                # 清理项目构建产物（删除 build/）
./gradlew build                # 编译并构建整个项目
./gradlew assembleDebug        # 生成 Debug APK
./gradlew assembleRelease      # 生成 Release APK（需签名配置）

## 🔹 基本构建
./gradlew --refresh-dependencies   # 忽略缓存，强制重新下载依赖
./gradlew dependencies             # 查看依赖树
